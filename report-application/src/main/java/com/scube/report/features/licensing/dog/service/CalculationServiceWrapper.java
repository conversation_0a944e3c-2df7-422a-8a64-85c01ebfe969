package com.scube.report.features.licensing.dog.service;

import com.scube.calculation.dto.order.gen_dto.OrderInvoiceItem;
import com.scube.calculation.dto.order.gen_dto.OrderInvoiceResponse;
import com.scube.client.calculation.generated.CalculationServiceConnection;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
public class CalculationServiceWrapper {
    private final CalculationServiceConnection calculationService;

    public List<OrderInvoiceResponse> getRegularDogOrders(Map<String, Object> params) {
        var responses = calculationService.order().findOrdersByFiltersAsync(
                new HashMap<>(Map.of(
                        "status", "ORDER_PAID",
                        "startDate", (String) params.get("startDate"),
                        "endDate", (String) params.get("endDate"),
                        "cartItemId", "license",
                        "cartItemName", "Dog License"))
        ).collectList().block();

        Assert.notNull(responses, "Responses cannot be null");

        // Iterate over each CartInvoiceResponse and filter the CartInvoiceItems
        responses.forEach(response -> {
            List<OrderInvoiceItem> filteredItems = response.getItems().stream()
                    .filter(item -> item.getPrimaryDisplay().startsWith("Dog License"))
                    .collect(Collectors.toList());
            response.setItems(filteredItems);
        });

        return responses;
    }

    public List<OrderInvoiceResponse> getPurebredDogOrders(Map<String, Object> params) {
        List<OrderInvoiceResponse> responses = calculationService.order().findOrdersByFiltersAsync(
                new HashMap<>(Map.of(
                        "status", "ORDER_PAID",
                        "startDate", (String) params.get("startDate"),
                        "endDate", (String) params.get("endDate"),
                        "cartItemId", "license",
                        "cartItemName", "Purebred"))
        ).collectList().block();

        // Iterate over each CartInvoiceResponse and filter the CartInvoiceItems
        responses.forEach(response -> {
            List<OrderInvoiceItem> filteredItems = response.getItems().stream()
                    .filter(item -> item.getPrimaryDisplay().startsWith("Purebred"))
                    .collect(Collectors.toList());
            response.setItems(filteredItems);
        });

        return responses;
    }

    public List<OrderInvoiceResponse> getDogTagOrders(Map<String, Object> params) {
        List<OrderInvoiceResponse> responses = calculationService.order().findOrdersByFiltersAsync(
                new HashMap<>(Map.of(
                        "status", "ORDER_PAID",
                        "startDate", (String) params.get("startDate"),
                        "endDate", (String) params.get("endDate"),
                        "cartItemId", "tag",
                        "cartItemName", "Dog Tag"))
        ).collectList().block();

        // Iterate over each CartInvoiceResponse and filter the CartInvoiceItems
        responses.forEach(response -> {
            List<OrderInvoiceItem> filteredItems = response.getItems().stream()
                    .filter(item -> item.getPrimaryDisplay().startsWith("Dog Tag"))
                    .collect(Collectors.toList());
            response.setItems(filteredItems);
        });

        return responses;
    }
}
