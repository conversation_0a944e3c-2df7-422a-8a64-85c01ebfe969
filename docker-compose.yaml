services:
  postgres:
    image: postgres:latest
    container_name: postgres-db
    hostname: postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: yourusername
      POSTGRES_PASSWORD: yourpassword
      POSTGRES_DB: auth
    networks:
      - backend

  rabbitmq:
    image: rabbitmq:3.11-management
    container_name: rabbitmq-dc
    hostname: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - backend

  keycloak:
    image: 514329541303.dkr.ecr.us-east-1.amazonaws.com/scube-keycloak:25.0.1-3.0.3
    container_name: keycloak-dc
    ports:
      - "8080:8080"
    environment:
      KEYCLOAK_ADMIN: yourusername
      KEYCLOAK_ADMIN_PASSWORD: yourpassword
      KC_PROXY: edge
      KC_DB: postgres
      KC_DB_URL: ************************************  # Use service name as hostname
      KC_DB_SCHEMA: keycloak
      KC_DB_USERNAME: yourusername
      KC_DB_PASSWORD: yourpassword
      KC_HOSTNAME_STRICT: "false"
      PROXY_ADDRESS_FORWARDING: "false"
      KEYCLOAK_FRONTEND_URL: https://host.docker.internal:3000/auth # Replace with actual frontend URL
      KEYCLOAK_ADMIN_URL: https://host.docker.internal:3000/auth/realms/master/admin/
      RABBITMQ_URL: rabbitmq:5672  # Use service name as hostname
      RABBITMQ_VHOST: /
      RABBITMQ_EXCHANGE: keycloak.events
      RABBITMQ_USERNAME: yourusername
      RABBITMQ_PASSWORD: yourpassword
    command: start-dev
    networks:
      - backend

networks:
  backend:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.host_binding_ipv4: "127.0.0.1"
