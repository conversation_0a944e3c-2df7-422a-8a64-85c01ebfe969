apiVersion: apps/v1
kind: Deployment
metadata:
  name: scube-config-service-depl
  labels:
    app: scube-config-service
spec:
  selector:
    matchLabels:
      app: scube-config-service
  template:
    metadata:
      labels:
        app: scube-config-service
    spec:
      nodeSelector:
        subnet-type: private
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: scube-config-service
          image: service_config
          ports:
            - containerPort: 10001
          resources:
            requests:
              memory: "350Mi"
            limits:
              memory: "1Gi"
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-config-service-srv.backend.svc.cluster.local
              path: /api/config/actuator/health
              port: 10001
            initialDelaySeconds: 10
            periodSeconds: 10
          livenessProbe:
            tcpSocket:
              port: 10001
            initialDelaySeconds: 60
            periodSeconds: 30
          env:
          - name: TZ
            value: "America/New_York"
          - name: SPRING_PROFILES_ACTIVE
            valueFrom:
              secretKeyRef:
                key: spring-profile
                name: spring-profile
          - name: SPRING_DATASOURCE_USERNAME
            valueFrom:
              secretKeyRef:
                name: postgres-user-pass-secret
                key: username
          - name: SPRING_DATASOURCE_PASSWORD
            valueFrom:
              secretKeyRef:
                name: postgres-user-pass-secret
                key: password
          - name: SPRING_RABBITMQ_USERNAME
            valueFrom:
              secretKeyRef:
                name: rabbitmq-user-pass-secret
                key: username
          - name: SPRING_RABBITMQ_PASSWORD
            valueFrom:
              secretKeyRef:
                name: rabbitmq-user-pass-secret
                key: password
          - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
            valueFrom:
              secretKeyRef:
                name: keycloak-id-secret
                key: swagger-client-id
          - name: KEYCLOAK_ADMIN_CLIENT_ID
            valueFrom:
              secretKeyRef:
                name: keycloak-admin-secret
                key: admin-client-id
          - name: KEYCLOAK_ADMIN_CLIENT_SECRET
            valueFrom:
              secretKeyRef:
                name: keycloak-admin-secret
                key: admin-client-secret
          - name: ENCRYPTION_SECRET_KEY
            valueFrom:
              secretKeyRef:
                name: encryption-secret
                key: secret-Key
---
apiVersion: v1
kind: Service
metadata:
  name: scube-config-service-srv
  labels:
    app: scube-config-service
spec:
  selector:
    app: scube-config-service
  type: ClusterIP
  ports:
    - name: config-port
      protocol: TCP
      port: 10001
      targetPort: 10001
