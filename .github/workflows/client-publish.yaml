name: "Publish Client to AWS CodeArtifact"
run-name: Publish new client version to AWS CodeArtifact by @${{ github.actor }}

on:
  workflow_dispatch:
  push:
    # Specify only client tags to trigger the workflow
    tags: ["client-v*.*.*"]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  publish-client:
    uses: s-Cube-Enterprise/reusable-workflows/.github/workflows/publish-client.yaml@main
    with:
      JAVA_VERSION: 21
    secrets: inherit
