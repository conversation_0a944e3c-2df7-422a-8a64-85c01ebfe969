name: "Development: Update, Build & Deploy"
run-name: Deploy ${{ github.repository }} to development by @${{ github.actor }}

on:
  workflow_dispatch:
  push:
    branches: [ "main" ]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true
  
jobs:
  development-build-deploy:
    uses: s-Cube-Enterprise/reusable-workflows/.github/workflows/js-build-publish.yaml@main
    with:
      AWS_REGION: us-east-1
      ECR_REPOSITORY: service_document_template_helper
      EKS_DEPLOYMENT: scube-document-template-helper-service-depl
      EKS_CONTAINER: scube-document-template-helper-service
      KUBE_NAMESPACE: backend
      IMAGE_TAG: latest
      NODE_VERSION: 16
      TARGET_ENVIRONMENT: DEV
    secrets: inherit
