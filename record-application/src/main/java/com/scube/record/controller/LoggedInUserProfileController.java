package com.scube.record.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.record.features.profile.dto.ProfileHeaderResponse;
import com.scube.record.features.profile.dto.ProfileRequest;
import com.scube.record.features.profile.dto.ProfileResponse;
import com.scube.record.features.profile.service.ProfileService;
import com.scube.record.features.profile.service.RejectedFieldsService;
import com.scube.record.features.user_registration.validation.CreateRecordIfNotExists;
import com.scube.record.permission.Permissions;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;
import java.util.UUID;

@RestController
@RequestMapping("/me/profiles")
@RequiredArgsConstructor
@Validated
@Slf4j
@Tag(name = "Logged In User Profile Management", description = "Profile management operations for logged-in users")
public class LoggedInUserProfileController {

    private final ProfileService profileService;
    private final RejectedFieldsService rejectedFieldsService;

    @GetMapping("/individual")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get individual record and all associations",
               description = "Get the individual record for the logged-in user with all associations. Creates record if it doesn't exist.")
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_PROFILE)
    public JsonNode getIndividualRecordAndAllAssociations(@AuthenticationPrincipal @CreateRecordIfNotExists MyOpenIdClaimSet jwt) {
        UUID userId = UUID.fromString(jwt.getSubject());
        log.info("Getting individual record and associations for user: {}", userId);
        return profileService.getRecordProfileAndAssociationsAsJsonByUuid(userId);
    }

    @PostMapping
    @Operation(summary = "Get record profile", description = "Get detailed profile of a record including associations and related records")
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_PROFILE)
    public ResponseEntity<ProfileResponse> getProfile(@RequestBody ProfileRequest request) {
        ProfileResponse response = profileService.getProfile(request);
        return ResponseEntity.ok(response);
    }


    @GetMapping("/uuid/{recordUuid}")
    @Operation(summary = "Get record profile by UUID", description = "Get detailed profile of a record by its UUID")
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_PROFILE_BY_UUID)
    public ResponseEntity<ProfileResponse> getProfileByUuid(
            @Parameter(description = "Record UUID") @PathVariable String recordUuid,
            @Parameter(description = "Include associations") @RequestParam(defaultValue = "true") boolean includeAssociations,
            @Parameter(description = "Include related records") @RequestParam(defaultValue = "false") boolean includeRelatedRecords,
            @Parameter(description = "Maximum relation depth") @RequestParam(defaultValue = "1") int maxRelationDepth) {

        ProfileRequest request = new ProfileRequest();
        request.setRecordUuid(recordUuid);
        request.setIncludeAssociations(includeAssociations);
        request.setIncludeRelatedRecords(includeRelatedRecords);
        request.setMaxRelationDepth(maxRelationDepth);

        ProfileResponse response = profileService.getProfile(request);
        return ResponseEntity.ok(response);
    }

   
    @GetMapping("/detail/{recordUuid}")
    @Operation(summary = "Get record profile by UUID with JSON response",
            description = "Get detailed profile of any record type by UUID with associations as JSON")
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_PROFILE_DETAIL_BY_UUID)
    public JsonNode getRecordProfileDetailByUuid(
            @Parameter(description = "Record UUID", example = "550e8400-e29b-41d4-a716-************")
            @PathVariable @NotNull UUID recordUuid) {

        log.info("Getting record profile detail for UUID: {}", recordUuid);
        return profileService.getRecordProfileAndAssociationsAsJsonByUuid(recordUuid);
    }

    @PatchMapping("/{recordUuid}/reject-fields")
    @ResponseStatus(HttpStatus.NO_CONTENT)
    @Operation(summary = "Add fields to rejected list",
            description = "Mark one or more fields as rejected for a record using UUID")
    @RolesAllowed(Permissions.LoggedInUserProfile.UPDATE_REJECT_FIELD)
    public void addToRejectedFieldList(
            @PathVariable @NotNull UUID recordUuid,
            @RequestBody RejectFieldRequest request) {

        log.info("Adding rejected fields for UUID: {}, fields: {}", recordUuid, request.fields());
        rejectedFieldsService.addToRejectedFieldListByUuid(recordUuid, request.fields(), false);
    }

   
    @GetMapping("/{recordUuid}/reject-fields")
    @ResponseStatus(HttpStatus.OK)
    @Operation(summary = "Get rejected fields")
    @RolesAllowed(Permissions.LoggedInUserProfile.GET_REJECTED_FIELDS)
    public RejectFieldResponse getRejectedFieldList(
            @PathVariable @NotNull UUID recordUuid) {

        var fields = rejectedFieldsService.getRejectedFieldListByUuid(recordUuid);
        return new RejectFieldResponse(fields);
    }

    public record RejectFieldRequest(Set<String> fields) {}
    public record RejectFieldResponse(Set<String> fields) {}
}
