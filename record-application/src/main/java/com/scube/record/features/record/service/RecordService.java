package com.scube.record.features.record.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.scube.record.features.record.dto.CreateRecordRequest;
import com.scube.record.features.record.dto.RecordResponse;
import com.scube.record.features.record.dto.UpdateRecordRequest;
import com.scube.record.features.record.exception.InvalidRecordTypeException;
import com.scube.record.features.record.exception.RecordNotFoundException;
import com.scube.record.features.record.mapper.RecordMapper;
import com.scube.record.infrastructure.db.entity.record.Record;
import com.scube.record.infrastructure.db.entity.record.RecordType;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import com.scube.record.infrastructure.db.repository.record.RecordTypeRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Transactional
public class RecordService {

    private final RecordRepository recordRepository;
    private final RecordTypeRepository recordTypeRepository;
    private final RecordMapper recordMapper;
    private final RecordAssociationService recordAssociationService;
    private final ObjectMapper objectMapper;

    /**
     * Normalizes the record properties based on the record_type's properties structure.
     * Ensures that every field defined in the record_type's properties exists in the record,
     * filling in null values for missing fields.
     *
     * @param recordTypeProperties The properties structure from record_type table
     * @param requestProperties The properties provided in the request
     * @return Normalized JsonNode with all required fields
     */
    private JsonNode normalizeProperties(JsonNode recordTypeProperties, JsonNode requestProperties) {
        // If record type has no properties structure, return the request properties as-is
        if (recordTypeProperties == null || recordTypeProperties.isNull() || !recordTypeProperties.isObject()) {
            return requestProperties;
        }

        ObjectNode normalizedProperties = objectMapper.createObjectNode();

        // Iterate through all fields defined in the record_type's properties
        Iterator<String> fieldNames = recordTypeProperties.fieldNames();
        while (fieldNames.hasNext()) {
            String fieldName = fieldNames.next();

            // Check if the field exists in the request properties
            if (requestProperties != null && requestProperties.has(fieldName)) {
                // Field exists in request, use the provided value
                normalizedProperties.set(fieldName, requestProperties.get(fieldName));
            } else {
                // Field missing in request, set to null
                normalizedProperties.putNull(fieldName);
            }
        }

        return normalizedProperties;
    }

    public RecordResponse createRecord(CreateRecordRequest request) {
        RecordType recordType = recordTypeRepository.findByTypeCode(request.getRecordTypeCode())
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + request.getRecordTypeCode()));

        Record record = recordMapper.toEntity(request);
        record.setRecordType(recordType);

        // Normalize properties based on record_type structure
        JsonNode normalizedProperties = normalizeProperties(
            recordType.getProperties(),
            request.getProperties()
        );
        record.setProperties(normalizedProperties);

        Record savedRecord = recordRepository.save(record);

        // Create associations if provided
        recordAssociationService.createAssociations(savedRecord, request.getAssociations(), request.getCreatedBy());

        return recordMapper.toResponse(savedRecord);
    }

    public RecordResponse updateRecord(UUID recordUuid, UpdateRecordRequest request) {
        Record record = recordRepository.findByRecordUuid(recordUuid)
            .orElseThrow(() -> new RecordNotFoundException(
                "Record not found with UUID: " + recordUuid));

        recordMapper.updateEntity(record, request);

        // Normalize properties based on record_type structure if properties are being updated
        if (request.getProperties() != null && record.getRecordType() != null) {
            JsonNode normalizedProperties = normalizeProperties(
                record.getRecordType().getProperties(),
                request.getProperties()
            );
            record.setProperties(normalizedProperties);
        }

        Record updatedRecord = recordRepository.save(record);
        return recordMapper.toResponse(updatedRecord);
    }

    @Transactional(readOnly = true)
    public RecordResponse getRecordByUuid(UUID recordUuid) {
        Record record = recordRepository.findByRecordUuid(recordUuid)
            .orElseThrow(() -> new RecordNotFoundException(
                "Record not found with UUID: " + recordUuid));
        return recordMapper.toResponse(record);
    }

    @Transactional(readOnly = true)
    public Optional<RecordResponse> findRecordByUuid(UUID recordUuid) {
        return recordRepository.findByRecordUuid(recordUuid)
            .map(recordMapper::toResponse);
    }

    @Transactional(readOnly = true)
    public Page<RecordResponse> getAllRecords(Pageable pageable) {
        Page<Record> records = recordRepository.findAll(pageable);
        List<RecordResponse> responses = records.getContent().stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, records.getTotalElements());
    }

    @Transactional(readOnly = true)
    public List<RecordResponse> getRecordsByType(String recordTypeCode) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));

        return recordRepository.findByRecordType(recordType).stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<RecordResponse> getRecordsByStatus(String status) {
        return recordRepository.findByStatus(status).stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Page<RecordResponse> getRecordsByTypeAndStatus(String recordTypeCode, String status, Pageable pageable) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));

        Page<Record> records = recordRepository.findByRecordTypeAndStatus(recordType, status, pageable);
        List<RecordResponse> responses = records.getContent().stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, records.getTotalElements());
    }

    @Transactional(readOnly = true)
    public Page<RecordResponse> searchRecords(String keyword, Pageable pageable) {
        Page<Record> records = recordRepository.searchByKeyword(keyword, pageable);
        List<RecordResponse> responses = records.getContent().stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
        return new PageImpl<>(responses, pageable, records.getTotalElements());
    }

    @Transactional(readOnly = true)
    public List<RecordResponse> searchRecordsByName(String recordName) {
        return recordRepository.findByRecordNameContaining(recordName).stream()
            .map(recordMapper::toResponse)
            .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public List<String> getRecordNameSuggestions(String query, int limit) {
        return recordRepository.findRecordNameSuggestions(query, limit);
    }

    @Transactional(readOnly = true)
    public List<String> getRecordNameSuggestionsByType(String query, String recordTypeCode, int limit) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));

        return recordRepository.findRecordNameSuggestionsByType(query, recordType.getRecordTypeId(), limit);
    }

    public void deleteRecord(UUID recordUuid) {
        if (!recordRepository.existsByRecordUuid(recordUuid)) {
            throw new RecordNotFoundException("Record not found with UUID: " + recordUuid);
        }
        recordRepository.deleteByRecordUuid(recordUuid);
    }

    @Transactional(readOnly = true)
    public boolean existsByUuid(UUID recordUuid) {
        return recordRepository.existsByRecordUuid(recordUuid);
    }

    @Transactional(readOnly = true)
    public long countRecords() {
        return recordRepository.count();
    }

    @Transactional(readOnly = true)
    public long countRecordsByType(String recordTypeCode) {
        RecordType recordType = recordTypeRepository.findByTypeCode(recordTypeCode)
            .orElseThrow(() -> new InvalidRecordTypeException(
                "Record type not found with code: " + recordTypeCode));
        return recordRepository.findByRecordType(recordType).size();
    }

    @Transactional(readOnly = true)
    public long countRecordsByStatus(String status) {
        return recordRepository.findByStatus(status).size();
    }

    @Transactional(readOnly = true)
    public List<RecordResponse> getRecordFees(UUID recordUuid) {
        // Check if the record exists first
        Record record = recordRepository.findByRecordUuid(recordUuid)
            .orElseThrow(() -> new RecordNotFoundException(
                "Record not found with UUID: " + recordUuid));

        // For now, return an empty list since fee functionality may need to be implemented
        // This method should be enhanced based on your fee system requirements
        // Example: Find associated fee records or fee structures for this record
        return List.of();
    }
}