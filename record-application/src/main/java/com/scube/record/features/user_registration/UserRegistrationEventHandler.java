package com.scube.record.features.user_registration;

import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Handler for UserRegistrationEvent from RabbitMQ.
 * Listens to the UserRegistrationEvent fanout exchange published by Service_Auth.
 *
 * When a user registers in Keycloak:
 * 1. Keycloak publishes event to topic exchange "keycloak.events"
 * 2. Service_Auth enriches it and publishes to fanout exchange
 * 3. This handler receives the event and creates an INDIVIDUAL record
 */
@Slf4j
@Component
public class UserRegistrationEventHandler extends FanoutListener<UserRegistrationEvent> {

    private final UserRegistrationService userRegistrationService;

    public UserRegistrationEventHandler(UserRegistrationService userRegistrationService) {
        this.userRegistrationService = userRegistrationService;
        log.warn("========================================");
        log.warn("UserRegistrationEventHandler INITIALIZED");
        log.warn("Event type: {}", UserRegistrationEvent.class.getSimpleName());
        log.warn("Handler class: {}", this.getClass().getSimpleName());
        log.warn("========================================");
    }

    /**
     * This method is automatically invoked by the scube-rabbitmq library when a
     * UserRegistrationEvent is published to the fanout exchange by Service_Auth.
     *
     * The FanoutListener base class handles:
     * - Queue creation: RecordServiceUserRegistrationEvent
     * - Exchange creation: UserRegistrationEventFanoutExchange
     * - Binding between queue and exchange
     * - Message deserialization and routing
     *
     * @param event The user registration event containing firstName, lastName, email, and userId
     */
    @Override
    public void consume(UserRegistrationEvent event) {
        log.warn("========================================");
        log.warn("=== UserRegistrationEventHandler.consume() CALLED ===");
        log.warn("Event: {}", event);
        log.warn("========================================");
        try {
            userRegistrationService.registerUser(event);
            log.info("=== Successfully processed user registration event for: {} ===", event.getEmail());
        } catch (Exception e) {
            log.error("========================================");
            log.error("Error processing user registration event: {}", event, e);
            log.error("========================================");
            // Don't rethrow - we don't want the message to be requeued
            // Error is already logged in the service layer
        }
    }
}