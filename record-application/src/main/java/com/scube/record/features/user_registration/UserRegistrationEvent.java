package com.scube.record.features.user_registration;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserRegistrationEvent implements Serializable, IRabbitFanoutSubscriber {

    @Serial
    private static final long serialVersionUID = -2192461924304841222L;
    private String firstName;
    private String lastName;
    private String email;
    private UUID userId;
}