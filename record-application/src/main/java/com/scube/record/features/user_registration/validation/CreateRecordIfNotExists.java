package com.scube.record.features.user_registration.validation;

import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.record.features.user_registration.UserRegistrationEvent;
import com.scube.record.features.user_registration.UserRegistrationService;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import jakarta.validation.Constraint;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import jakarta.validation.Payload;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.UUID;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = CreateRecordIfNotExists.CheckRecordExistValidator.class)
public @interface CreateRecordIfNotExists {
    String message() default "Individual record does not exist. Consider creating one first.";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Slf4j
    @RequiredArgsConstructor
    class CheckRecordExistValidator implements ConstraintValidator<CreateRecordIfNotExists, MyOpenIdClaimSet> {
        private final RecordRepository recordRepository;
        private final UserRegistrationService userRegistrationService;

        @Override
        public void initialize(CreateRecordIfNotExists constraintAnnotation) {
            ConstraintValidator.super.initialize(constraintAnnotation);
        }

        @Override
        public boolean isValid(MyOpenIdClaimSet claimSet, ConstraintValidatorContext constraintValidatorContext) {
            if (claimSet == null) {
                log.warn("MyOpenIdClaimSet is null, skipping record creation");
                return true;
            }

            try {
                var entityId = UUID.fromString(claimSet.getSubject());
                var exists = recordRepository.existsByRecordUuid(entityId);
                
                if (!exists) {
                    log.info("No individual record found for user ID: {}, creating new record", entityId);
                    var event = new UserRegistrationEvent(
                        claimSet.getGivenName(), 
                        claimSet.getFamilyName(), 
                        claimSet.getEmail(), 
                        entityId
                    );
                    
                    try {
                        userRegistrationService.registerUser(event);
                        log.info("Successfully created individual record for user: {}", entityId);
                    } catch (Exception e) {
                        log.error("Failed to create individual record for user registration event: {}", event, e);
                        throw e;
                    }
                } else {
                    log.debug("Individual record already exists for user ID: {}", entityId);
                }
                
                return true;
            } catch (IllegalArgumentException e) {
                log.error("Invalid UUID format in subject claim: {}", claimSet.getSubject(), e);
                return true; // Don't fail validation due to invalid UUID format
            } catch (Exception e) {
                log.error("Unexpected error during record validation for user: {}", claimSet.getSubject(), e);
                throw e;
            }
        }
    }
}
