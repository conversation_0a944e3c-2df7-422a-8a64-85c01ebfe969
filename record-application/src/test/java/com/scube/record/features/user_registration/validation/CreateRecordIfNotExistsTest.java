package com.scube.record.features.user_registration.validation;

import com.scube.auth.library.MyOpenIdClaimSet;
import com.scube.record.features.user_registration.UserRegistrationEvent;
import com.scube.record.features.user_registration.UserRegistrationService;
import com.scube.record.infrastructure.db.repository.record.RecordRepository;
import jakarta.validation.ConstraintValidatorContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CreateRecordIfNotExistsTest {

    @Mock
    private RecordRepository recordRepository;

    @Mock
    private UserRegistrationService userRegistrationService;

    @Mock
    private ConstraintValidatorContext constraintValidatorContext;

    @Mock
    private MyOpenIdClaimSet claimSet;

    private CreateRecordIfNotExists.CheckRecordExistValidator validator;

    @BeforeEach
    void setUp() {
        validator = new CreateRecordIfNotExists.CheckRecordExistValidator(recordRepository, userRegistrationService);
    }

    @Test
    void shouldReturnTrueWhenClaimSetIsNull() {
        // When
        boolean result = validator.isValid(null, constraintValidatorContext);

        // Then
        assertTrue(result);
        verifyNoInteractions(recordRepository, userRegistrationService);
    }

    @Test
    void shouldReturnTrueWhenRecordAlreadyExists() {
        // Given
        UUID userId = UUID.randomUUID();
        when(claimSet.getSubject()).thenReturn(userId.toString());
        when(recordRepository.existsByRecordUuid(userId)).thenReturn(true);

        // When
        boolean result = validator.isValid(claimSet, constraintValidatorContext);

        // Then
        assertTrue(result);
        verify(recordRepository).existsByRecordUuid(userId);
        verifyNoInteractions(userRegistrationService);
    }

    @Test
    void shouldCreateRecordWhenRecordDoesNotExist() {
        // Given
        UUID userId = UUID.randomUUID();
        String givenName = "John";
        String familyName = "Doe";
        String email = "<EMAIL>";

        when(claimSet.getSubject()).thenReturn(userId.toString());
        when(claimSet.getGivenName()).thenReturn(givenName);
        when(claimSet.getFamilyName()).thenReturn(familyName);
        when(claimSet.getEmail()).thenReturn(email);
        when(recordRepository.existsByRecordUuid(userId)).thenReturn(false);

        // When
        boolean result = validator.isValid(claimSet, constraintValidatorContext);

        // Then
        assertTrue(result);
        verify(recordRepository).existsByRecordUuid(userId);

        ArgumentCaptor<UserRegistrationEvent> eventCaptor = ArgumentCaptor.forClass(UserRegistrationEvent.class);
        verify(userRegistrationService).registerUser(eventCaptor.capture());

        UserRegistrationEvent capturedEvent = eventCaptor.getValue();
        assertEquals(givenName, capturedEvent.getFirstName());
        assertEquals(familyName, capturedEvent.getLastName());
        assertEquals(email, capturedEvent.getEmail());
        assertEquals(userId, capturedEvent.getUserId());
    }

    @Test
    void shouldReturnTrueWhenInvalidUuidFormat() {
        // Given
        when(claimSet.getSubject()).thenReturn("invalid-uuid");

        // When
        boolean result = validator.isValid(claimSet, constraintValidatorContext);

        // Then
        assertTrue(result);
        verifyNoInteractions(recordRepository, userRegistrationService);
    }

    @Test
    void shouldPropagateExceptionWhenUserRegistrationFails() {
        // Given
        UUID userId = UUID.randomUUID();
        when(claimSet.getSubject()).thenReturn(userId.toString());
        when(claimSet.getGivenName()).thenReturn("John");
        when(claimSet.getFamilyName()).thenReturn("Doe");
        when(claimSet.getEmail()).thenReturn("<EMAIL>");
        when(recordRepository.existsByRecordUuid(userId)).thenReturn(false);
        
        RuntimeException expectedException = new RuntimeException("Registration failed");
        doThrow(expectedException).when(userRegistrationService).registerUser(any(UserRegistrationEvent.class));

        // When & Then
        RuntimeException thrownException = assertThrows(RuntimeException.class, () -> 
            validator.isValid(claimSet, constraintValidatorContext)
        );
        
        assertEquals(expectedException, thrownException);
        verify(userRegistrationService).registerUser(any(UserRegistrationEvent.class));
    }
}
