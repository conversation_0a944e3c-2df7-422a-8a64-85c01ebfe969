const express = require("express");
const templateRouter = require("./routes/template");
const healthRouter = require("./routes/health");
const bodyParser = require("body-parser");
const swagger = require("./swagger");

const app = express();

//set global context path
const apiRouter = express.Router();

swagger(apiRouter);

//--max-http-header-size=30000

apiRouter.use(bodyParser.json({ limit: "50mb" }));
apiRouter.use("/template", templateRouter);
apiRouter.use("/actuator", healthRouter);

app.use("/api/document-template-helper", apiRouter);

app.listen(9012, () => {
    console.log(
        "Your Server is running on 9012: http://localhost:9012/api/document-template-helper/swagger-ui/index.html"
    );
});

app.use((err, req, res, next) => {
    res.render("error", { error: err });
});
