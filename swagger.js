const swaggerJsdoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");

const options = {
    definition: {
        info: {
            title: "Document Template Helper API",
            version: "1.0.0",
            description: "This service is used to generate document template using easy-template-x",
        },
        // Add basePath property
        basePath: "/api/document-template-helper",
    },
    apis: ["routes/*.js"],
};

const specs = swaggerJsdoc(options);

module.exports = (app) => {
    app.use("/swagger-ui/index.html", swaggerUi.serve, swaggerUi.setup(specs));
};
