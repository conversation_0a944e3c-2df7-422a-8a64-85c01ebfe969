apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/ai-service-sa-role
  labels:
    app.kubernetes.io/managed-by: eksctl
  name: ai-service-sa
  namespace: backend
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-ai-service
  name: scube-ai-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-ai-service
  template:
    metadata:
      labels:
        app: scube-ai-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: OPEN_AI_API_TOKEN
              valueFrom:
                secretKeyRef:
                  key: token
                  name: open-ai-token
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_ai:latest@sha256:7dcb14df37d24560703ba9d352b487e77f5f005a61fbfcd9635cb154a45a8faa
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9011
          name: scube-ai-service
          ports:
            - containerPort: 9011
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-ai-service-srv.backend.svc.cluster.local
              path: /api/ai/actuator/health
              port: 9011
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
      serviceAccountName: ai-service-sa
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-ai-service
  name: scube-ai-service-srv
  namespace: backend
spec:
  ports:
    - name: ai-port
      port: 9011
      protocol: TCP
      targetPort: 9011
  selector:
    app: scube-ai-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-auth-service
  name: scube-auth-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-auth-service
  template:
    metadata:
      labels:
        app: scube-auth-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_auth:latest@sha256:fa64889114fcad5756cdc85ec16bf0e690167a4bfeaa877ae9231485dc60f69e
          livenessProbe:
            initialDelaySeconds: 30
            periodSeconds: 30
            tcpSocket:
              port: 9001
          name: scube-auth-service
          ports:
            - containerPort: 9001
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-auth-service-srv.backend.svc.cluster.local
              path: /api/auth/actuator/health
              port: 9001
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-auth-service
  name: scube-auth-service-srv
  namespace: backend
spec:
  ports:
    - name: auth-port
      port: 9001
      protocol: TCP
      targetPort: 9001
  selector:
    app: scube-auth-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-calculation-service
  name: scube-calculation-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-calculation-service
  template:
    metadata:
      labels:
        app: scube-calculation-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_calculation:latest@sha256:d7a42341bfe49c3d46beb6bf67fed0be8c65e915a1f83371234db31210d05e2f
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9002
          name: scube-calculation-service
          ports:
            - containerPort: 9002
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-calculation-service-srv.backend.svc.cluster.local
              path: /api/calculation/actuator/health
              port: 9002
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-calculation-service
  name: scube-calculation-service-srv
  namespace: backend
spec:
  ports:
    - name: calc-port
      port: 9002
      protocol: TCP
      targetPort: 9002
  selector:
    app: scube-calculation-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-config-service
  name: scube-config-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-config-service
  template:
    metadata:
      labels:
        app: scube-config-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_config:latest@sha256:ce97e4f1023589ed30298b90459f2be3c25bc51ae27948b72390965952656883
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 10001
          name: scube-config-service
          ports:
            - containerPort: 10001
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-config-service-srv.backend.svc.cluster.local
              path: /api/config/actuator/health
              port: 10001
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-config-service
  name: scube-config-service-srv
  namespace: backend
spec:
  ports:
    - name: config-port
      port: 10001
      protocol: TCP
      targetPort: 10001
  selector:
    app: scube-config-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-coordinator-service
  name: scube-coordinator-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-coordinator-service
  template:
    metadata:
      labels:
        app: scube-coordinator-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_coordinator:latest@sha256:1238487481a8fc22f7a5133a2ba8653f5d59148ace5342a837b65cf0f60996f3
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 10004
          name: scube-coordinator-service
          ports:
            - containerPort: 10004
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-coordinator-service-srv.backend.svc.cluster.local
              path: /api/coordinator/actuator/health
              port: 10004
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-coordinator-service
  name: scube-coordinator-service-srv
  namespace: backend
spec:
  ports:
    - name: crdinator-port
      port: 10004
      protocol: TCP
      targetPort: 10004
  selector:
    app: scube-coordinator-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-document-template-helper-service
  name: scube-document-template-helper-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-document-template-helper-service
  template:
    metadata:
      labels:
        app: scube-document-template-helper-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_document_template_helper:latest@sha256:423831bbf5f6b3002e5ca5e96769d677ef1a41881d98d19a4503a594347172e9
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9012
          name: scube-document-template-helper-service
          ports:
            - containerPort: 9012
          readinessProbe:
            httpGet:
              path: /api/document-template-helper/actuator/health
              port: 9012
            initialDelaySeconds: 30
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-document-template-helper-service
  name: scube-document-template-helper-service-srv
  namespace: backend
spec:
  ports:
    - name: temp-help-port
      port: 9012
      protocol: TCP
      targetPort: 9012
  selector:
    app: scube-document-template-helper-service
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/LicensingS3Role
  labels:
    app.kubernetes.io/managed-by: eksctl
  name: s3-account
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-document-service
  name: scube-document-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-document-service
  template:
    metadata:
      labels:
        app: scube-document-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_document:latest@sha256:534212477d821549842fb46e8ec2df9f49961ef2f6a32af7ba6584198182cf25
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9003
          name: scube-document-service
          ports:
            - containerPort: 9003
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-document-service-srv.backend.svc.cluster.local
              path: /api/document-service/actuator/health
              port: 9003
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
      serviceAccountName: s3-account
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-document-service
  name: scube-document-service-srv
  namespace: backend
spec:
  ports:
    - name: document-port
      port: 9003
      protocol: TCP
      targetPort: 9003
  selector:
    app: scube-document-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-document-template-service
  name: scube-document-template-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-document-template-service
  template:
    metadata:
      labels:
        app: scube-document-template-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_document_template:latest@sha256:61d05a8764d91e2ebc1ef405adc8857ba1441a0755a01927f0ca8910b01b0b3f
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9009
          name: scube-document-template-service
          ports:
            - containerPort: 9009
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-document-template-service-srv.backend.svc.cluster.local
              path: /api/document-template/actuator/health
              port: 9009
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-document-template-service
  name: scube-document-template-service-srv
  namespace: backend
spec:
  ports:
    - name: template-port
      port: 9009
      protocol: TCP
      targetPort: 9009
  selector:
    app: scube-document-template-service
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  annotations:
    eks.amazonaws.com/role-arn: arn:aws:iam::************:role/textract-sa-role
  labels:
    app.kubernetes.io/managed-by: eksctl
  name: textract-service-account
  namespace: backend
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-imageprocessing-service
  name: scube-imageprocessing-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-imageprocessing-service
  template:
    metadata:
      labels:
        app: scube-imageprocessing-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_imageprocessing:latest@sha256:734379f54f64b129528f5e24982ba1016e8d38ea03f1834526fe8701eb67b2ca
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9010
          name: scube-imageprocessing-service
          ports:
            - containerPort: 9010
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-imageprocessing-service-srv.backend.svc.cluster.local
              path: /api/image-processing/actuator/health
              port: 9010
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
      serviceAccountName: textract-service-account
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-imageprocessing-service
  name: scube-imageprocessing-service-srv
  namespace: backend
spec:
  ports:
    - name: processing-port
      port: 9010
      protocol: TCP
      targetPort: 9010
  selector:
    app: scube-imageprocessing-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-license-service
  name: scube-license-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-license-service
  template:
    metadata:
      labels:
        app: scube-license-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: GOOGLE_MAPS_API_KEY
              valueFrom:
                secretKeyRef:
                  key: key
                  name: google-maps-api-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_license:latest@sha256:1fdd87364088b204739df8a5060e6cf1c4c2ad52f64b5984d06a713d829abca1
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9004
          name: scube-license-service
          ports:
            - containerPort: 9004
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-license-service-srv.backend.svc.cluster.local
              path: /api/license/actuator/health
              port: 9004
            initialDelaySeconds: 30
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-license-service
  name: scube-license-service-srv
  namespace: backend
spec:
  ports:
    - name: license-port
      port: 9004
      protocol: TCP
      targetPort: 9004
  selector:
    app: scube-license-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-notification-service
  name: scube-notification-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-notification-service
  template:
    metadata:
      labels:
        app: scube-notification-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: TWILIO_ACCOUNT_SID
              valueFrom:
                secretKeyRef:
                  key: sid
                  name: twilio-credentials
            - name: TWILIO_AUTH_TOKEN
              valueFrom:
                secretKeyRef:
                  key: token
                  name: twilio-credentials
            - name: TWILIO_FROM_NUMBER
              valueFrom:
                secretKeyRef:
                  key: from-number
                  name: twilio-credentials
            - name: SENDGRID_API_KEY
              valueFrom:
                secretKeyRef:
                  key: token
                  name: sendgrid-credentials
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
            - name: SMTP_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: smtp-user-pass-secret
            - name: SMTP_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: smtp-user-pass-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_notification:latest@sha256:74b588dcee2a09fa672ca9c1e6e35f97541f8d0b80104d13a74657baf08985f0
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9005
          name: scube-notification-service
          ports:
            - containerPort: 9005
          readinessProbe:
            exec:
              command:
                - curl
                - -X
                - GET
                - http://localhost:9005/api/notification/actuator/health
            initialDelaySeconds: 30
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-notification-service
  name: scube-notification-service-srv
  namespace: backend
spec:
  ports:
    - name: notify-port
      port: 9005
      protocol: TCP
      targetPort: 9005
  selector:
    app: scube-notification-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-ocr-service
  name: scube-ocr-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-ocr-service
  template:
    metadata:
      labels:
        app: scube-ocr-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_ocr:latest@sha256:ea364dae1cf21420ee639de4cd0b957363d2da821675620e9efe33248a6854be
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9008
          name: scube-ocr-service
          ports:
            - containerPort: 9008
          readinessProbe:
            httpGet:
              path: /api/ocr/actuator/health
              port: 9008
            initialDelaySeconds: 30
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-ocr-service
  name: scube-ocr-service-srv
  namespace: backend
spec:
  ports:
    - name: ocr-port
      port: 9008
      protocol: TCP
      targetPort: 9008
  selector:
    app: scube-ocr-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-payment-service
  name: scube-payment-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-payment-service
  template:
    metadata:
      labels:
        app: scube-payment-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
            - name: AUTHORIZE_DOT_NET_API_LOGIN_ID
              valueFrom:
                secretKeyRef:
                  key: api-login-id
                  name: authorize-dot-net-secrets
            - name: AUTHORIZE_DOT_NET_TRANSACTION_KEY
              valueFrom:
                secretKeyRef:
                  key: transaction-key
                  name: authorize-dot-net-secrets
            - name: AUTHORIZE_DOT_NET_MERCHANT_SIGNATURE_KEY
              valueFrom:
                secretKeyRef:
                  key: signature-key
                  name: authorize-dot-net-secrets
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_payment:latest@sha256:fae8605018b92472f5e20bb2d5b702ef7dea757d6c95b2aacc37eade88a0af0e
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9006
          name: scube-payment-service
          ports:
            - containerPort: 9006
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-payment-service-srv.backend.svc.cluster.local
              path: /api/payment/actuator/health
              port: 9006
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-payment-service
  name: scube-payment-service-srv
  namespace: backend
spec:
  ports:
    - name: payment-port
      port: 9006
      protocol: TCP
      targetPort: 9006
  selector:
    app: scube-payment-service
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: scube-report-service
  name: scube-report-service-depl
  namespace: backend
spec:
  selector:
    matchLabels:
      app: scube-report-service
  template:
    metadata:
      labels:
        app: scube-report-service
    spec:
      containers:
        - env:
            - name: TZ
              value: America/New_York
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                secretKeyRef:
                  key: spring-profile
                  name: spring-profile
            - name: SPRING_DATASOURCE_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: SPRING_DATASOURCE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_SWAGGER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: swagger-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-id
                  name: keycloak-id-secret
            - name: KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: server-to-server-client-secret
                  name: keycloak-id-secret
            - name: KEYCLOAK_ADMIN_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  key: admin-client-id
                  name: keycloak-admin-secret
            - name: KEYCLOAK_ADMIN_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  key: admin-client-secret
                  name: keycloak-admin-secret
          image: ************.dkr.ecr.us-east-1.amazonaws.com/service_report:latest@sha256:831ce562e3bcc3d97cccd27c97ce6cebb05b4ea33fcf895e7cc0ee3767aa38a2
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 9007
          name: scube-report-service
          ports:
            - containerPort: 9007
          readinessProbe:
            httpGet:
              httpHeaders:
                - name: Host
                  value: scube-report-service-srv.backend.svc.cluster.local
              path: /api/report/actuator/health
              port: 9007
            initialDelaySeconds: 10
            periodSeconds: 10
          resources:
            limits:
              memory: 1Gi
            requests:
              memory: 350Mi
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: scube-report-service
  name: scube-report-service-srv
  namespace: backend
spec:
  ports:
    - name: report-port
      port: 9007
      protocol: TCP
      targetPort: 9007
  selector:
    app: scube-report-service
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-agent-injector
  name: clerkxpress-backend-vault-agent-injector
  namespace: hashicorp-vault
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault
    helm.sh/chart: vault-0.24.1
  name: clerkxpress-backend-vault
  namespace: hashicorp-vault
---
apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: mongodb
    app.kubernetes.io/version: 4.4.5
    helm.sh/chart: mongodb-10.12.4
  name: clerkxpress-backend-mongodb
  namespace: vizivault
secrets:
  - name: clerkxpress-backend-mongodb
---
apiVersion: v1
data:
  mongodb-password: PDwgcmVwbGFjZSBtZSA+Pg==
  mongodb-root-password: QU5rSVloT2JZNA==
kind: Secret
metadata:
  labels:
    app.kubernetes.io/component: mongodb
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: mongodb
    app.kubernetes.io/version: 4.4.5
    helm.sh/chart: mongodb-10.12.4
  name: clerkxpress-backend-mongodb
  namespace: vizivault
type: Opaque
---
apiVersion: v1
data:
  rabbitmq-password: PDwgcmVwbGFjZSBtZSA+Pg==
  token-signing-key: PDwgcmVwbGFjZSBtZSA+Pg==
  vizivault-admin-password: PDwgcmVwbGFjZSBtZSA+Pg==
  vizivault-exchange-token: Y2QxM2UzMzYtNmU0NS00NDAyLTlkYmItNzA0NWQ0MDgyZTc1
kind: Secret
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vizivault-platform-secrets
    helm.sh/chart: vizivault-platform-0.6.1
  name: vizivault-platform-secrets
  namespace: vizivault
type: Opaque
---
apiVersion: v1
data:
  KC_DB: postgres
  KC_DB_SCHEMA: keycloak
  KC_DB_URL: *****************************************
  KC_HOSTNAME_STRICT: "false"
  KC_PROXY: edge
  KEYCLOAK_ADMIN_URL: https://auth-dev.clerkxpress.com/auth/realms/master/admin/
  KEYCLOAK_FRONTEND_URL: https://auth-dev.clerkxpress.com/auth
  KK_TO_RMQ_EXCHANGE: keycloak.events
  KK_TO_RMQ_URL: rabbitmq.backend.svc.cluster.local
  KK_TO_RMQ_VHOST: /
  PROXY_ADDRESS_FORWARDING: "false"
kind: ConfigMap
metadata:
  labels:
    group: auth
  name: keycloak-config
  namespace: keycloak
---
apiVersion: v1
data:
  rabbitmq.conf: |
    management.path_prefix = /rabbitmq/admin
    log.console = true
kind: ConfigMap
metadata:
  labels:
    group: messaging
  name: rabbitmq-config
  namespace: backend
---
apiVersion: v1
data:
  extraconfig-from-values.hcl: |-
    disable_mlock = true
    ui = true

    listener "tcp" {
      tls_disable = 1
      address = "[::]:8200"
      cluster_address = "[::]:8201"
      # Enable unauthenticated metrics access (necessary for Prometheus Operator)
      #telemetry {
      #  unauthenticated_metrics_access = "true"
      #}
    }
    storage "file" {
      path = "/vault/data"
    }

    # Example configuration for using auto-unseal, using Google Cloud KMS. The
    # GKMS keys must already exist, and the cluster must have a service account
    # that is authorized to access GCP KMS.
    #seal "gcpckms" {
    #   project     = "vault-helm-dev"
    #   region      = "global"
    #   key_ring    = "vault-helm-unseal-kr"
    #   crypto_key  = "vault-helm-unseal-key"
    #}

    # Example configuration for enabling Prometheus metrics in your config.
    #telemetry {
    #  prometheus_retention_time = "30s"
    #  disable_hostname = true
    #}
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault
    helm.sh/chart: vault-0.24.1
  name: clerkxpress-backend-vault-config
  namespace: hashicorp-vault
---
apiVersion: v1
binaryData: null
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vizivault-certs
    helm.sh/chart: vizivault-platform-0.6.1
  name: vizivault-certs
  namespace: vizivault
---
apiVersion: v1
data:
  grantroles.js: |
    use admin
    db.grantRolesToUser(
      "vizivault-platform",
      [
        { role: "readWrite", db: "webapp" },
        { role: "readWrite", db: "vault" },
        { role: "readWrite", db: "arbiter" },
        { role: "readWrite", db: "alerts" },
      ]
    )
kind: ConfigMap
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vizivault-platform-initdb
    helm.sh/chart: vizivault-platform-0.6.1
  name: vizivault-platform-initdb
  namespace: vizivault
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-agent-injector
  name: clerkxpress-backend-vault-agent-injector-clusterrole
rules:
  - apiGroups:
      - admissionregistration.k8s.io
    resources:
      - mutatingwebhookconfigurations
    verbs:
      - get
      - list
      - watch
      - patch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-agent-injector
  name: clerkxpress-backend-vault-agent-injector-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: clerkxpress-backend-vault-agent-injector-clusterrole
subjects:
  - kind: ServiceAccount
    name: clerkxpress-backend-vault-agent-injector
    namespace: hashicorp-vault
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault
    helm.sh/chart: vault-0.24.1
  name: clerkxpress-backend-vault-server-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:auth-delegator
subjects:
  - kind: ServiceAccount
    name: clerkxpress-backend-vault
    namespace: hashicorp-vault
---
apiVersion: v1
kind: Service
metadata:
  labels:
    group: auth
  name: keycloak
  namespace: keycloak
spec:
  ports:
    - name: keycloak-port
      port: 8080
      targetPort: 8080
  selector:
    app: keycloak
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    group: messaging
  name: rabbitmq
  namespace: backend
spec:
  ports:
    - name: amqp
      port: 5672
      targetPort: 5672
    - name: http
      port: 15672
      targetPort: 15672
  selector:
    app: rabbitmq
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-agent-injector
  name: clerkxpress-backend-vault-agent-injector-svc
  namespace: hashicorp-vault
spec:
  ports:
    - name: https
      port: 443
      targetPort: 8080
  selector:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/name: vault-agent-injector
    component: webhook
---
apiVersion: v1
kind: Service
metadata:
  annotations: null
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault
    helm.sh/chart: vault-0.24.1
    vault-internal: "true"
  name: clerkxpress-backend-vault-internal
  namespace: hashicorp-vault
spec:
  clusterIP: None
  ports:
    - name: http
      port: 8200
      targetPort: 8200
    - name: https-internal
      port: 8201
      targetPort: 8201
  publishNotReadyAddresses: true
  selector:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/name: vault
    component: server
---
apiVersion: v1
kind: Service
metadata:
  annotations: null
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault
    helm.sh/chart: vault-0.24.1
  name: clerkxpress-backend-vault
  namespace: hashicorp-vault
spec:
  ports:
    - name: http
      port: 8200
      targetPort: 8200
    - name: https-internal
      port: 8201
      targetPort: 8201
  publishNotReadyAddresses: true
  selector:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/name: vault
    component: server
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/component: mongodb
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: mongodb
    app.kubernetes.io/version: 4.4.5
    helm.sh/chart: mongodb-10.12.4
  name: clerkxpress-backend-mongodb
  namespace: vizivault
spec:
  ports:
    - name: mongodb
      nodePort: null
      port: 27017
      targetPort: mongodb
  selector:
    app.kubernetes.io/component: mongodb
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/name: mongodb
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: clerkxpress-backend
    helm.sh/chart: vizivault-platform-0.6.1
  name: vizivault-platform-alerts-service
  namespace: vizivault
spec:
  ports:
    - name: vizivault-alerts-http
      port: 80
      protocol: TCP
      targetPort: http
    - name: vizivault-alerts-ws
      port: 81
      protocol: TCP
      targetPort: ws
  selector:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/name: vizivault-alerts-vizivault
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: clerkxpress-backend
    helm.sh/chart: vizivault-platform-0.6.1
  name: vizivault-platform-api-service
  namespace: vizivault
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
  selector:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/name: vizivault-api-vizivault
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: clerkxpress-backend
    helm.sh/chart: vizivault-platform-0.6.1
  name: vizivault-platform-arbiter-service
  namespace: vizivault
spec:
  ports:
    - name: vizivault-arbiter
      port: 50051
      protocol: TCP
      targetPort: grpc
  selector:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/name: vizivault-arbiter-vizivault
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: clerkxpress-backend
    helm.sh/chart: vizivault-platform-0.6.1
  name: vizivault-platform-cipher-service
  namespace: vizivault
spec:
  ports:
    - name: vizivault-cipher
      port: 50051
      protocol: TCP
      targetPort: grpc
  selector:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/name: vizivault-cipher-vizivault
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: clerkxpress-backend
    helm.sh/chart: vizivault-platform-0.6.1
  name: vizivault-platform-web-service
  namespace: vizivault
spec:
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
  selector:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/name: vizivault-webapp
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: keycloak
    group: auth
  name: keycloak
  namespace: keycloak
spec:
  replicas: 1
  selector:
    matchLabels:
      app: keycloak
  template:
    metadata:
      labels:
        app: keycloak
        group: auth
    spec:
      containers:
        - args:
            - start --http-port=8080
          env:
            - name: KK_TO_RMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: KK_TO_RMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: KEYCLOAK_USER
              valueFrom:
                secretKeyRef:
                  key: username
                  name: keycloak-user-pass-secret
            - name: KEYCLOAK_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: keycloak-user-pass-secret
            - name: KC_DB_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: postgres-user-pass-secret
            - name: KC_DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: postgres-user-pass-secret
          envFrom:
            - configMapRef:
                name: keycloak-config
          image: ************.dkr.ecr.us-east-1.amazonaws.com/scube-keycloak:25.0.1-3.0.3
          name: keycloak
          ports:
            - containerPort: 8080
          readinessProbe:
            httpGet:
              path: /realms/master
              port: 8080
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-agent-injector
    component: webhook
  name: clerkxpress-backend-vault-agent-injector
  namespace: hashicorp-vault
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: clerkxpress-backend
      app.kubernetes.io/name: vault-agent-injector
      component: webhook
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: clerkxpress-backend
        app.kubernetes.io/name: vault-agent-injector
        component: webhook
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchLabels:
                  app.kubernetes.io/instance: clerkxpress-backend
                  app.kubernetes.io/name: vault-agent-injector
                  component: webhook
              topologyKey: kubernetes.io/hostname
      containers:
        - args:
            - agent-inject
            - 2>&1
          env:
            - name: AGENT_INJECT_LISTEN
              value: :8080
            - name: AGENT_INJECT_LOG_LEVEL
              value: info
            - name: AGENT_INJECT_VAULT_ADDR
              value: http://clerkxpress-backend-vault.hashicorp-vault.svc:8200
            - name: AGENT_INJECT_VAULT_AUTH_PATH
              value: auth/kubernetes
            - name: AGENT_INJECT_VAULT_IMAGE
              value: hashicorp/vault:1.13.1
            - name: AGENT_INJECT_TLS_AUTO
              value: clerkxpress-backend-vault-agent-injector-cfg
            - name: AGENT_INJECT_TLS_AUTO_HOSTS
              value: clerkxpress-backend-vault-agent-injector-svc,clerkxpress-backend-vault-agent-injector-svc.hashicorp-vault,clerkxpress-backend-vault-agent-injector-svc.hashicorp-vault.svc
            - name: AGENT_INJECT_LOG_FORMAT
              value: standard
            - name: AGENT_INJECT_REVOKE_ON_SHUTDOWN
              value: "false"
            - name: AGENT_INJECT_CPU_REQUEST
              value: 250m
            - name: AGENT_INJECT_CPU_LIMIT
              value: 275m
            - name: AGENT_INJECT_MEM_REQUEST
              value: 64Mi
            - name: AGENT_INJECT_MEM_LIMIT
              value: 128Mi
            - name: AGENT_INJECT_DEFAULT_TEMPLATE
              value: map
            - name: AGENT_INJECT_TEMPLATE_CONFIG_EXIT_ON_RETRY_FAILURE
              value: "true"
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          image: hashicorp/vault-k8s:1.2.1
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 2
            httpGet:
              path: /health/ready
              port: 8080
              scheme: HTTPS
            initialDelaySeconds: 5
            periodSeconds: 2
            successThreshold: 1
            timeoutSeconds: 5
          name: sidecar-injector
          readinessProbe:
            failureThreshold: 2
            httpGet:
              path: /health/ready
              port: 8080
              scheme: HTTPS
            initialDelaySeconds: 5
            periodSeconds: 2
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: 250m
              memory: 256Mi
            requests:
              cpu: 250m
              memory: 256Mi
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
                - ALL
          startupProbe:
            failureThreshold: 12
            httpGet:
              path: /health/ready
              port: 8080
              scheme: HTTPS
            initialDelaySeconds: 5
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
      hostNetwork: false
      imagePullSecrets:
        - name: aws-ecr-secret
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 100
      serviceAccountName: clerkxpress-backend-vault-agent-injector
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: alerts-vizivault
    helm.sh/chart: vizivault-platform-0.6.1
  name: clerkxpress-backend-alerts-vizivault
  namespace: vizivault
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: clerkxpress-backend
      app.kubernetes.io/name: vizivault-alerts-vizivault
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: clerkxpress-backend
        app.kubernetes.io/name: vizivault-alerts-vizivault
    spec:
      containers:
        - env:
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: mongodb-password
                  name: clerkxpress-backend-mongodb
            - name: MONGO_URI
              value: mongodb://vizivault-platform:$(DATABASE_PASSWORD)@clerkxpress-backend-mongodb:27017/alerts-vizivault?authSource=admin
            - name: SIGNING_KEY_PATH
              value: key/secret.key
          image: ************.dkr.ecr.us-east-1.amazonaws.com/vizivault-alerts:0.1.0
          imagePullPolicy: IfNotPresent
          livenessProbe:
            httpGet:
              path: /health
              port: http
          name: vizivault-platform
          ports:
            - containerPort: 3000
              name: http
              protocol: TCP
            - containerPort: 3001
              name: ws
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /health
              port: http
          resources: null
          volumeMounts:
            - mountPath: /usr/src/app/key
              name: credentials
              readOnly: true
            - mountPath: /certs
              name: certs
      imagePullSecrets:
        - name: aws-ecr-secret
      initContainers:
        - args:
            - wait-for-it
            - -h
            - clerkxpress-backend-mongodb
            - -p
            - "27017"
            - -t
            - "180"
          image: ************.dkr.ecr.us-east-1.amazonaws.com/vizivault-initializer:0.2.2
          name: initialize-alerts
      volumes:
        - name: credentials
          secret:
            items:
              - key: token-signing-key
                path: secret.key
            secretName: vizivault-platform-secrets
        - configMap:
            defaultMode: 420
            name: vizivault-certs
          name: certs
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: api-vizivault
    helm.sh/chart: vizivault-platform-0.6.1
  name: clerkxpress-backend-api-vizivault
  namespace: vizivault
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: clerkxpress-backend
      app.kubernetes.io/name: vizivault-api-vizivault
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: clerkxpress-backend
        app.kubernetes.io/name: vizivault-api-vizivault
    spec:
      containers:
        - env:
            - name: SERVER_SERVLET_CONTEXTPATH
              value: /vizivault
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: mongodb-password
                  name: clerkxpress-backend-mongodb
            - name: SPRING_DATA_MONGODB_URI
              value: mongodb://vizivault-platform:$(DATABASE_PASSWORD)@clerkxpress-backend-mongodb:27017/vault-vizivault?authSource=admin
            - name: SPRING_DATA_MONGODB_DATABASE
              value: vault-vizivault
            - name: SPRING_RABBITMQ_HOST
              value: rabbitmq.backend.svc.cluster.local
            - name: SPRING_RABBITMQ_PORT
              value: "5672"
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_VIRTUAL_HOST
              value: /
            - name: SPRING_RABBITMQ_SSL_ENABLED
              value: "false"
            - name: NOX_API_AUTHORIZATION
              value: "true"
            - name: NOX_KEYS_EXCHANGE_TOKEN
              valueFrom:
                secretKeyRef:
                  key: vizivault-exchange-token
                  name: vizivault-platform-secrets
            - name: CIPHER_HOST
              value: vizivault-platform-cipher-service
            - name: CIPHER_PORT
              value: "50051"
            - name: ARBITER_HOST
              value: vizivault-platform-arbiter-service
            - name: ARBITER_PORT
              value: "50051"
          image: ************.dkr.ecr.us-east-1.amazonaws.com/vizivault-api:0.1.0
          imagePullPolicy: IfNotPresent
          name: vizivault-api-vizivault
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          resources: null
      imagePullSecrets:
        - name: aws-ecr-secret
      initContainers:
        - args:
            - scripts/initialize-api
          env:
            - name: MONGO_HOST
              value: clerkxpress-backend-mongodb
            - name: MONGO_PORT
              value: "27017"
            - name: MONGO_USERNAME
              value: vizivault-platform
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: mongodb-password
                  name: clerkxpress-backend-mongodb
            - name: MONGO_AUTH_SOURCE
              value: admin
            - name: MONGO_SSL_ENABLED
              value: "false"
            - name: MONGO_NAMESPACE
              value: vizivault
            - name: RABBIT_HOST
              value: rabbitmq.backend.svc.cluster.local
            - name: RABBIT_PORT
              value: "5672"
          image: ************.dkr.ecr.us-east-1.amazonaws.com/vizivault-initializer:0.2.2
          name: initialize-api
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: arbiter-vizivault
    helm.sh/chart: vizivault-platform-0.6.1
  name: clerkxpress-backend-arbiter-vizivault
  namespace: vizivault
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: clerkxpress-backend
      app.kubernetes.io/name: vizivault-arbiter-vizivault
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: clerkxpress-backend
        app.kubernetes.io/name: vizivault-arbiter-vizivault
    spec:
      containers:
        - env:
            - name: HOSTNAME
              value: 0.0.0.0
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: mongodb-password
                  name: clerkxpress-backend-mongodb
            - name: MONGO_URI
              value: mongodb://vizivault-platform:$(DATABASE_PASSWORD)@clerkxpress-backend-mongodb:27017/?authSource=admin
            - name: MONGO_AUTH_DB
              value: webapp-vizivault
          image: ************.dkr.ecr.us-east-1.amazonaws.com/vizivault-arbiter:0.2.0
          imagePullPolicy: IfNotPresent
          livenessProbe:
            tcpSocket:
              port: grpc
          name: vizivault-platform
          ports:
            - containerPort: 50052
              name: grpc
              protocol: TCP
          readinessProbe:
            tcpSocket:
              port: grpc
          resources: null
          volumeMounts:
            - mountPath: /certs
              name: certs
      imagePullSecrets:
        - name: aws-ecr-secret
      initContainers:
        - args:
            - wait-for-it
            - -h
            - clerkxpress-backend-mongodb
            - -p
            - "27017"
            - -t
            - "180"
          env:
            - name: MONGO_SSL_ENABLED
              value: "false"
          image: ************.dkr.ecr.us-east-1.amazonaws.com/vizivault-initializer:0.2.2
          name: initialize-arbiter
      volumes:
        - configMap:
            defaultMode: 420
            name: vizivault-certs
          name: certs
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: cipher-vizivault
    helm.sh/chart: vizivault-platform-0.6.1
  name: clerkxpress-backend-cipher-vizivault
  namespace: vizivault
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: clerkxpress-backend
      app.kubernetes.io/name: vizivault-cipher-vizivault
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: clerkxpress-backend
        app.kubernetes.io/name: vizivault-cipher-vizivault
    spec:
      containers:
        - env:
            - name: HOSTNAME
              value: 0.0.0.0
          image: ************.dkr.ecr.us-east-1.amazonaws.com/vizivault-cipher:0.1.0
          imagePullPolicy: IfNotPresent
          livenessProbe:
            tcpSocket:
              port: grpc
          name: vizivault-platform
          ports:
            - containerPort: 50051
              name: grpc
              protocol: TCP
          readinessProbe:
            tcpSocket:
              port: grpc
          resources: null
      imagePullSecrets:
        - name: aws-ecr-secret
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: webapp
    helm.sh/chart: vizivault-platform-0.6.1
  name: clerkxpress-backend-webapp
  namespace: vizivault
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: clerkxpress-backend
      app.kubernetes.io/name: vizivault-webapp
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: clerkxpress-backend
        app.kubernetes.io/name: vizivault-webapp
    spec:
      containers:
        - env:
            - name: VIZIVAULT_ADMIN_USERNAME
              value: vizivault
            - name: VIZIVAULT_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: vizivault-admin-password
                  name: vizivault-platform-secrets
            - name: VIZIVAULT_ADMIN_EMAIL
              value: <EMAIL>
            - name: VIZIVAULT_ADMIN_NAME
              value: ViziVault Admin
            - name: VIZIVAULT_AUTHENTICATION_METHOD
              value: managed
            - name: SERVER_SERVLET_CONTEXTPATH
              value: /vizivault
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: mongodb-password
                  name: clerkxpress-backend-mongodb
            - name: SPRING_DATA_MONGODB_URI
              value: mongodb://vizivault-platform:$(DATABASE_PASSWORD)@clerkxpress-backend-mongodb:27017/webapp-vizivault?authSource=admin
            - name: SPRING_DATA_MONGODB_DATABASE
              value: webapp-vizivault
            - name: SPRING_RABBITMQ_HOST
              value: rabbitmq.backend.svc.cluster.local
            - name: SPRING_RABBITMQ_PORT
              value: "5672"
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
            - name: SPRING_RABBITMQ_VIRTUAL_HOST
              value: /
            - name: SPRING_RABBITMQ_SSL_ENABLED
              value: "false"
            - name: NOVA_SERVICE_URI
              value: https://clerkxpress.com
            - name: NOVA_BROADCAST_URI
              value: http://vizivault-platform-alerts-service
            - name: NOVA_SIGNING_KEY
              valueFrom:
                secretKeyRef:
                  key: token-signing-key
                  name: vizivault-platform-secrets
          image: ************.dkr.ecr.us-east-1.amazonaws.com/vizivault-webapp:0.2.2-rc.3
          imagePullPolicy: IfNotPresent
          name: vizivault-webapp
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          resources: null
      imagePullSecrets:
        - name: aws-ecr-secret
      initContainers:
        - args:
            - scripts/initialize-vizivault
          env:
            - name: MONGO_HOST
              value: clerkxpress-backend-mongodb
            - name: MONGO_PORT
              value: "27017"
            - name: MONGO_USERNAME
              value: vizivault-platform
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: mongodb-password
                  name: clerkxpress-backend-mongodb
            - name: MONGO_AUTH_SOURCE
              value: admin
            - name: MONGO_SSL_ENABLED
              value: "false"
            - name: MONGO_NAMESPACE
              value: vizivault
            - name: RABBIT_HOST
              value: rabbitmq.backend.svc.cluster.local
            - name: RABBIT_PORT
              value: "5672"
          image: ************.dkr.ecr.us-east-1.amazonaws.com/vizivault-initializer:0.2.2
          name: initialize-webapp
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: rabbitmq
    group: messaging
  name: rabbitmq
  namespace: backend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: rabbitmq
      group: messaging
  serviceName: rabbitmq
  template:
    metadata:
      labels:
        app: rabbitmq
        app.kubernetes.io/instance: clerkxpress-backend
        group: messaging
    spec:
      containers:
        - env:
            - name: RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  key: username
                  name: rabbitmq-user-pass-secret
            - name: RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: password
                  name: rabbitmq-user-pass-secret
          envFrom:
            - configMapRef:
                name: rabbitmq-config
          image: rabbitmq:3.11.9-management
          imagePullPolicy: Always
          name: rabbitmq
          ports:
            - containerPort: 5672
              name: amqp
            - containerPort: 15672
              name: http
          resources:
            limits:
              cpu: 500m
              memory: 1024Mi
            requests:
              cpu: 250m
              memory: 512Mi
          volumeMounts:
            - mountPath: /var/lib/rabbitmq/data
              name: rabbitmq-pvc
      imagePullSecrets:
        - name: aws-ecr-secret
      volumes:
        - name: rabbitmq-pvc
          persistentVolumeClaim:
            claimName: rabbitmq-pvc
  volumeClaimTemplates:
    - metadata:
        name: rabbitmq-pvc
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 2Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault
  name: clerkxpress-backend-vault
  namespace: hashicorp-vault
spec:
  podManagementPolicy: Parallel
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/instance: clerkxpress-backend
      app.kubernetes.io/name: vault
      component: server
  serviceName: clerkxpress-backend-vault-internal
  template:
    metadata:
      labels:
        app.kubernetes.io/instance: clerkxpress-backend
        app.kubernetes.io/name: vault
        component: server
        helm.sh/chart: vault-0.24.1
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchLabels:
                  app.kubernetes.io/instance: clerkxpress-backend
                  app.kubernetes.io/name: vault
                  component: server
              topologyKey: kubernetes.io/hostname
      containers:
        - args:
            - "cp /vault/config/extraconfig-from-values.hcl /tmp/storageconfig.hcl;\n[ -n \"${HOST_IP}\" ] && sed -Ei \"s|HOST_IP|${HOST_IP?}|g\" /tmp/storageconfig.hcl;\n[ -n \"${POD_IP}\" ] && sed -Ei \"s|POD_IP|${POD_IP?}|g\" /tmp/storageconfig.hcl;\n[ -n \"${HOSTNAME}\" ] && sed -Ei \"s|HOSTNAME|${HOSTNAME?}|g\" /tmp/storageconfig.hcl;\n[ -n \"${API_ADDR}\" ] && sed -Ei \"s|API_ADDR|${API_ADDR?}|g\" /tmp/storageconfig.hcl;\n[ -n \"${TRANSIT_ADDR}\" ] && sed -Ei \"s|TRANSIT_ADDR|${TRANSIT_ADDR?}|g\" /tmp/storageconfig.hcl;\n[ -n \"${RAFT_ADDR}\" ] && sed -Ei \"s|RAFT_ADDR|${RAFT_ADDR?}|g\" /tmp/storageconfig.hcl;\n/usr/local/bin/docker-entrypoint.sh vault server -config=/tmp/storageconfig.hcl \n"
          command:
            - /bin/sh
            - -ec
          env:
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: VAULT_K8S_POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: VAULT_K8S_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: VAULT_ADDR
              value: http://127.0.0.1:8200
            - name: VAULT_API_ADDR
              value: http://$(POD_IP):8200
            - name: SKIP_CHOWN
              value: "true"
            - name: SKIP_SETCAP
              value: "true"
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: VAULT_CLUSTER_ADDR
              value: https://$(HOSTNAME).clerkxpress-backend-vault-internal:8201
            - name: HOME
              value: /home/<USER>
          image: hashicorp/vault:1.13.1
          imagePullPolicy: IfNotPresent
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - sleep 5 && kill -SIGTERM $(pidof vault)
          name: vault
          ports:
            - containerPort: 8200
              name: http
            - containerPort: 8201
              name: https-internal
            - containerPort: 8202
              name: http-rep
          readinessProbe:
            exec:
              command:
                - /bin/sh
                - -ec
                - vault status -tls-skip-verify
            failureThreshold: 2
            initialDelaySeconds: 5
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 3
          resources:
            limits:
              cpu: 1250m
              memory: 2Gi
            requests:
              cpu: 1000m
              memory: 1Gi
          securityContext:
            allowPrivilegeEscalation: false
          volumeMounts:
            - mountPath: /vault/data
              name: data
            - mountPath: /vault/config
              name: config
            - mountPath: /home/<USER>
              name: home
      hostNetwork: false
      imagePullSecrets:
        - name: aws-ecr-secret
      securityContext:
        fsGroup: 1000
        runAsGroup: 1000
        runAsNonRoot: true
        runAsUser: 100
      serviceAccountName: clerkxpress-backend-vault
      terminationGracePeriodSeconds: 10
      volumes:
        - configMap:
            name: clerkxpress-backend-vault-config
          name: config
        - emptyDir: {}
          name: home
  updateStrategy:
    type: OnDelete
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 10Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app.kubernetes.io/component: mongodb
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: mongodb
    app.kubernetes.io/version: 4.4.5
    helm.sh/chart: mongodb-10.12.4
  name: clerkxpress-backend-mongodb
  namespace: vizivault
spec:
  selector:
    matchLabels:
      app.kubernetes.io/component: mongodb
      app.kubernetes.io/instance: clerkxpress-backend
      app.kubernetes.io/name: mongodb
  serviceName: clerkxpress-backend-mongodb
  template:
    metadata:
      labels:
        app.kubernetes.io/component: mongodb
        app.kubernetes.io/instance: clerkxpress-backend
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/name: mongodb
        app.kubernetes.io/version: 4.4.5
        helm.sh/chart: mongodb-10.12.4
    spec:
      affinity:
        nodeAffinity: null
        podAffinity: null
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - podAffinityTerm:
                labelSelector:
                  matchLabels:
                    app.kubernetes.io/component: mongodb
                    app.kubernetes.io/instance: clerkxpress-backend
                    app.kubernetes.io/name: mongodb
                topologyKey: kubernetes.io/hostname
              weight: 1
      containers:
        - env:
            - name: BITNAMI_DEBUG
              value: "false"
            - name: MONGODB_USERNAME
              value: vizivault-platform
            - name: MONGODB_DATABASE
              value: admin
            - name: MONGODB_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: mongodb-password
                  name: clerkxpress-backend-mongodb
            - name: MONGODB_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  key: mongodb-root-password
                  name: clerkxpress-backend-mongodb
            - name: ALLOW_EMPTY_PASSWORD
              value: "no"
            - name: MONGODB_SYSTEM_LOG_VERBOSITY
              value: "0"
            - name: MONGODB_DISABLE_SYSTEM_LOG
              value: "no"
            - name: MONGODB_DISABLE_JAVASCRIPT
              value: "no"
            - name: MONGODB_ENABLE_IPV6
              value: "no"
            - name: MONGODB_ENABLE_DIRECTORY_PER_DB
              value: "no"
          image: docker.io/bitnami/mongodb:4.4.5-debian-10-r0
          imagePullPolicy: IfNotPresent
          livenessProbe:
            exec:
              command:
                - mongo
                - --disableImplicitSessions
                - --eval
                - db.adminCommand('ping')
            failureThreshold: 6
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          name: mongodb
          ports:
            - containerPort: 27017
              name: mongodb
          readinessProbe:
            exec:
              command:
                - bash
                - -ec
                - |
                  mongo --disableImplicitSessions $TLS_OPTIONS --eval 'db.hello().isWritablePrimary || db.hello().secondary' | grep -q 'true'
            failureThreshold: 6
            initialDelaySeconds: 5
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits: {}
            requests: {}
          securityContext:
            runAsNonRoot: true
            runAsUser: 1001
          volumeMounts:
            - mountPath: /bitnami/mongodb
              name: datadir
              subPath: null
            - mountPath: /docker-entrypoint-initdb.d
              name: custom-init-scripts
      imagePullSecrets:
        - name: aws-ecr-secret
      initContainers:
        - args:
            - |
              mkdir -p /bitnami/mongodb
              chown -R "1001:1001" "/bitnami/mongodb"
          command:
            - /bin/bash
            - -ec
          image: docker.io/bitnami/bitnami-shell:10
          imagePullPolicy: Always
          name: volume-permissions
          resources:
            limits: {}
            requests: {}
          securityContext:
            runAsUser: 0
          volumeMounts:
            - mountPath: /bitnami/mongodb
              name: datadir
      securityContext:
        fsGroup: 1001
        sysctls: []
      serviceAccountName: clerkxpress-backend-mongodb
      volumes:
        - configMap:
            name: vizivault-platform-initdb
          name: custom-init-scripts
  updateStrategy:
    type: RollingUpdate
  volumeClaimTemplates:
    - metadata:
        name: datadir
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 8Gi
---
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  labels:
    app.kubernetes.io/instance: clerkxpress-backend
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: vault-agent-injector
  name: clerkxpress-backend-vault-agent-injector-cfg
webhooks:
  - admissionReviewVersions:
      - v1
      - v1beta1
    clientConfig:
      caBundle: ""
      service:
        name: clerkxpress-backend-vault-agent-injector-svc
        namespace: hashicorp-vault
        path: /mutate
    failurePolicy: Ignore
    matchPolicy: Exact
    name: vault.hashicorp.com
    objectSelector:
      matchExpressions:
        - key: app.kubernetes.io/name
          operator: NotIn
          values:
            - vault-agent-injector
    rules:
      - apiGroups:
          - ""
        apiVersions:
          - v1
        operations:
          - CREATE
          - UPDATE
        resources:
          - pods
    sideEffects: None
    timeoutSeconds: 30
---
# Source: clerkxpress-backend/charts/vault/templates/tests/server-test.yaml
apiVersion: v1
kind: Pod
metadata:
  name: "clerkxpress-backend-server-test"
  namespace: hashicorp-vault
  annotations:
    "helm.sh/hook": test
spec:
  imagePullSecrets:
    - name: aws-ecr-secret
  containers:
    - name: clerkxpress-backend-server-test
      image: hashicorp/vault:1.13.1
      imagePullPolicy: IfNotPresent
      env:
        - name: VAULT_ADDR
          value: http://clerkxpress-backend-vault.hashicorp-vault.svc:8200
        
      command:
        - /bin/sh
        - -c
        - |
          echo "Checking for sealed info in 'vault status' output"
          ATTEMPTS=10
          n=0
          until [ "$n" -ge $ATTEMPTS ]
          do
            echo "Attempt" $n...
            vault status -format yaml | grep -E '^sealed: (true|false)' && break
            n=$((n+1))
            sleep 5
          done
          if [ $n -ge $ATTEMPTS ]; then
            echo "timed out looking for sealed info in 'vault status' output"
            exit 1
          fi

          exit 0
      volumeMounts:
  volumes:
  restartPolicy: Never
