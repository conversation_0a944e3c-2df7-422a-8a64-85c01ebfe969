# Deployments

Complete Kubernetes YAMLs for each level. Don't modify these by hand, create/update them by running:

```shell
# Development
skaffold render -d 514329541303.dkr.ecr.us-east-1.amazonaws.com --output deployments/development.yaml -n backend -p aws-development -t latest

# Staging
skaffold render -d 514329541303.dkr.ecr.us-east-1.amazonaws.com --output deployments/staging.yaml -n backend -p aws-staging -t staging

# Production
skaffold render -d 514329541303.dkr.ecr.us-east-1.amazonaws.com --output deployments/production.yaml -n backend -p aws-production -t latest
```

or by running the `create-deployments.bat`/`create-deployments.sh` scripts in the root directory.
