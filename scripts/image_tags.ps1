<#
.SYNOPSIS
Generates a list of currently deployed images and their tagged verions.

.DESCRIP<PERSON><PERSON>
Creates a text file with the ouput for use or sharing purposes with the option to open the file immediately, if desired.

.PARAMETER OpenWith
Boolean to state if you want to open the created file right away.

.PARAMETER Namespace
String used to query specific Kubernetes namespaces for images/versions. Defaults to --all-namespaces if not specified.

.EXAMPLE
.\image_tags.ps1
Runs the script with open prompted disabled and all namespaces (default behavior).

.EXAMPLE
.\image_tags.ps1 -OpenWith $true
Runs with the Open With prompt.

.EXAMPLE
.\image_tags.ps1 -Namespace "backend"
Runs against the backend namespace.
#>

param(
    [bool]$OpenWith = $false,
    [String]$Namespace
)

# Query pods and save information
if ($Namespace -ne "") {
    Write-Host "Using namespace $Namespace"
    $list = (kubectl get pods -n $Namespace -o jsonpath="{.items[*].spec.containers[*].image}")
} else {
    Write-Host "Using --all-namespaces"
    $list = (kubectl get pods --all-namespaces -o jsonpath="{.items[*].spec.containers[*].image}")
}

# Split results into a readable format and route to text file
$list -split ' ' > .\current_versions.txt

if ($OpenWith) {
    OpenWith.exe .\current_versions.txt
}

Write-Host "Done"
