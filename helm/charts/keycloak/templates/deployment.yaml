apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.name }}
  namespace: {{ .Values.namespace }}
  labels:
    app: {{ .Values.name }}
    group: {{ .Values.group }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.name }}
        group: {{ .Values.group }}
    spec:
      nodeSelector:
        subnet-type: private
      imagePullSecrets:
        - name: {{ .Values.imagePullSecrets }}
      containers:
        - name: {{ .Values.name }}
          args: {{ .Values.args }}
          image: {{ .Values.container.image }}
          ports:
            - containerPort: {{ .Values.container.port }}
          envFrom:
            - configMapRef:
                name: {{ .Values.config.name }}
          env:
            - name: {{ .Values.secrets.rmqUsername.name}}
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.rmqUsername.secretName }}
                  key: {{ .Values.secrets.rmqUsername.secretKey }}
            - name: {{ .Values.secrets.rmqPassword.name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.rmqPassword.secretName }}
                  key: {{ .Values.secrets.rmqPassword.secretKey }}
            - name: {{ .Values.secrets.user.name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.user.secretName }}
                  key: {{ .Values.secrets.user.secretKey }}
            - name: {{ .Values.secrets.password.name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.password.secretName }}
                  key: {{ .Values.secrets.password.secretKey }}
            - name: {{ .Values.secrets.dbuser.name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.dbuser.secretName }}
                  key: {{ .Values.secrets.dbuser.secretKey }}
            - name: {{ .Values.secrets.dbpassword.name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.dbpassword.secretName }}
                  key: {{ .Values.secrets.dbpassword.secretKey }}
          readinessProbe:
            httpGet:
              path: {{ .Values.deployment.readinessProbe }}
              port: {{ .Values.container.port }}
