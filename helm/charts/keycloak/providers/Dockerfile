# Run the following when updates to the Java version are needed for this project:
#     docker build -t 514329541303.dkr.ecr.us-east-1.amazonaws.com/scube-keycloak:25.0.1-3.0.3 .
#     docker push 514329541303.dkr.ecr.us-east-1.amazonaws.com/scube-keycloak:25.0.1-3.0.3
# local build:
#     docker build -t scube-keycloak:25.0.1-3.0.3 .

# Use the official Keycloak image as the base image
FROM quay.io/keycloak/keycloak:25.0.1

WORKDIR /opt/keycloak/data/password-blacklists/
COPY blacklist.txt .

# Set the working directory to /opt/keycloak/providers/
WORKDIR /opt/keycloak/providers/

# Copy the JAR file to the current directory
COPY keycloak-to-rabbit-3.0.3.jar .
