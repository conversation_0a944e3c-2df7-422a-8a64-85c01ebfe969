replicaCount: 1
keycloak:
  name: keycloak
  namespace: keycloak
  group: other
  args:
    - "start"
    - "--http-port=8080"
  container:
    image: 514329541303.dkr.ecr.us-east-1.amazonaws.com/scube-keycloak:25.0.1-3.0.3
    port: 8080
  deployment:
    readinessProbe: /realms/master
  service:
    name: keycloak-port
    type: ClusterIP
    port: 8080
  imagePullSecrets: aws-ecr-secret
  config:
    name: keycloak-config
    data:
      - key: KC_PROXY
        value: edge
      - key: KC_DB
        value: postgres
      - key: KC_DB_URL
        value: ************************************************
      - key: KC_DB_SCHEMA
        value: "keycloak"
      - key: KC_HOSTNAME_STRICT
        value: '"false"' # Renders wrong otherwise, must be Strings
      - key: PROXY_ADDRESS_FORWARDING
        value: '"false"' # Renders wrong otherwise, must be Strings
      - key: KEYCLOAK_FRONTEND_URL
        value: "https://staging-clerkxpress.com/auth"
      - key: <PERSON><PERSON>YCLOA<PERSON>_ADMIN_URL
        value: "https://staging-clerkxpress.com/auth/realms/master/admin/"
        # for keycloak-event-listener-rabbitmq
      - key: KK_TO_RMQ_URL
        value: "rabbitmq.backend.svc.cluster.local"
      - key: KK_TO_RMQ_VHOST
        value: "/"
      - key: KK_TO_RMQ_EXCHANGE
        value: "keycloak.events"
  secrets:
    rmqUsername:
      name: KK_TO_RMQ_USERNAME
      secretName: rabbitmq-user-pass-secret
      secretKey: username
    rmqPassword:
      name: KK_TO_RMQ_PASSWORD
      secretName: rabbitmq-user-pass-secret
      secretKey: password
    dbuser:
      name: KC_DB_USERNAME
      secretName: postgres-user-pass-secret
      secretKey: username
    dbpassword:
      name: KC_DB_PASSWORD
      secretName: postgres-user-pass-secret
      secretKey: password
    user:
      name: KEYCLOAK_USER
      secretName: keycloak-user-pass-secret
      secretKey: username
    password:
      name: KEYCLOAK_PASSWORD
      secretName: keycloak-user-pass-secret
      secretKey: password
