{{- $webAppUrl := "http://localhost" }}
{{- $apiAppUrl := "http://localhost" }}

You have successfully deployed the ViziVault Platform!

Please allow at least five minutes for the platform to become available after a fresh installation.

To log into the ViziVault Platform Web Application:

1. Obtain the URL

{{- if .Values.ingress.web.enabled }}
  {{- $webAppUrl = include "vizivault-platform.appUrl" . }}

  If properly configured, you may find the web application running at:
  {{ $webAppUrl }}

{{- else if contains "ClusterIP" .Values.vizivault.service.type }}
  {{- $webAppUrl = "http://127.0.0.1:8080" }}

  Port forward the application by running the following command:
  kubectl port-forward --namespace {{ .Release.Namespace }} svc/vizivault-platform-web-service 8080:8080

  Visit the application running at:
  {{ $webAppUrl }}

  #####################################################################################
  # WARNING: Realtime notifications / alerts will be unavailable when port forwarding #
  #####################################################################################

{{- else if contains "NodePort" .Values.vizivault.service.type  }}
  {{- $webAppUrl = "http://$VIZIVAULT_IP:$VIZIVAULT_PORT/" }}

  Determine the application address by running the following:
  export VIZIVAULT_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services vizivault-platform-web-service)
  export VIZIVAULT_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo "ViziVault Web URL: {{ $webAppUrl }}"

{{- end }}

2. Log in using the credentials below

  Username: {{ .Values.vizivault.admin.username }}
  Password: ********

  To view the admin password, run the following commands:
  VIZIVAULT_PASSWORD=$(kubectl get secret --namespace {{ .Release.Namespace }} vizivault-platform-secrets -o jsonpath="{.data.vizivault-admin-password}" | base64 --decode)
  echo $VIZIVAULT_PASSWORD


To access the Vizivault REST API:

1. Obtain the URL

{{- if .Values.ingress.api.enabled }}
  {{- $apiAppUrl = include "vizivault-platform.apiUrl" . }}

  If properly configured, you may find the API running at:
  {{ $apiAppUrl }}

{{- else if contains "ClusterIP" .Values.api.service.type }}
  {{- $apiAppUrl = "http://127.0.0.1:8081" }}

  Port forward the application by running the following command:
  kubectl port-forward --namespace {{ .Release.Namespace }} svc/vizivault-platform-api-service 8081:8080

  The API should now be reachable at:
  {{ $apiAppUrl }}

{{- else if contains "NodePort" .Values.vizivault.service.type  }}
  {{- $apiAppUrl = "http://$VIZIVAULT_IP:$VIZIVAULT_PORT/" }}

  Determine the API address by running the following:
  export VIZIVAULT_API_PORT=$(kubectl get --namespace {{ .Release.Namespace }} -o jsonpath="{.spec.ports[0].nodePort}" services vizivault-platform-api-service)
  export VIZIVAULT_API_IP=$(kubectl get nodes --namespace {{ .Release.Namespace }} -o jsonpath="{.items[0].status.addresses[0].address}")
  echo "ViziVault API URL: "{{ $apiAppUrl }}"

{{- end }}

2. Obtain a key pair for use with the API
  ################################################################################################
  # WARNING: The exchange token may only be used once, make sure to save the result!             #
  # Store the key pair securely and safely, loss of keys may result in a loss of data            #
  ################################################################################################

  Obtain the exchange token by running the command:
  VIZIVAULT_EXCHANGE_TOKEN=$(kubectl get secret --namespace {{ .Release.Namespace }} vizivault-platform-secrets -o jsonpath="{.data.vizivault-exchange-token}" | base64 --decode)
  echo $VIZIVAULT_EXCHANGE_TOKEN

  Exchange the token for a new key pair by issuing the following request:
  curl --location --request POST '{{ $apiAppUrl }}/keys/exchange' \
  --header 'Content-Type: application/json' \
  --data-raw "{ \"exchangeToken\": \"${VIZIVAULT_EXCHANGE_TOKEN}\" }" > vault-api-keys.json

  Upon success, the key pair will now be found in the file 'vault-api-keys.json'

3. Obtain a valid API Key from the Web Application

  As an administrator, create and obtain a new API Key at:
  {{ $webAppUrl }}/admin/applications

4. Begin using the REST API with the API key and generated key pair

  Additional documentation can be found at:
  https://docs.anontech.io/api/endpoints/


{{- include "common.warnings.rollingTag" .Values.vizivault.image }}
{{- include "common.warnings.rollingTag" .Values.api.image }}
{{- include "common.warnings.rollingTag" .Values.alerts.image }}
{{- include "common.warnings.rollingTag" .Values.cipher.image }}
{{- include "common.warnings.rollingTag" .Values.arbiter.image }}