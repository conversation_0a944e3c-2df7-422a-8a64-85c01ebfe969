apiVersion: v1
kind: ConfigMap
metadata:
  namespace: {{ .Values.global.namespace }}
  name: vizivault-platform-initdb
  labels:
    app.kubernetes.io/name: vizivault-platform-initdb
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
data:
  grantroles.js: |
    use admin
    db.grantRolesToUser(
      "vizivault-platform",
      [
        { role: "readWrite", db: "webapp" },
        { role: "readWrite", db: "vault" },
        { role: "readWrite", db: "arbiter" },
        { role: "readWrite", db: "alerts" },
      ]
    )
