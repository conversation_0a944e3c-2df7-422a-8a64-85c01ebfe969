apiVersion: v1
kind: Secret
metadata:
  namespace: {{ .Values.global.namespace }}
  name: vizivault-platform-secrets
  labels:
    app.kubernetes.io/name: vizivault-platform-secrets
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: Opaque
data:
  vizivault-admin-password: {{ b64enc (include "vizivault-platform.password" .) | quote }}
  vizivault-exchange-token: {{ uuidv4 | b64enc | quote }}
  token-signing-key: {{ b64enc (include "vizivault-platform.password" .) | quote }}
  {{- if and (not .Values.mongodb.enabled) .Values.database.password }}
  mongodb-password: {{ .Values.database.password | b64enc | quote }}
  {{- end }}
  {{- if and (not .Values.rabbitmq.enabled) .Values.rabbit.password }}
  rabbitmq-password: {{ .Values.rabbit.password | b64enc | quote }}
  {{- end }}
