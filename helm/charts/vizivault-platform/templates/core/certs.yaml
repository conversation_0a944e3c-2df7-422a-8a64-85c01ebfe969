apiVersion: v1
kind: ConfigMap
metadata:
  namespace: {{ .Values.global.namespace }}
  name: vizivault-certs
  labels:
    app.kubernetes.io/name: vizivault-certs
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
binaryData:
  {{- if .Values.database.certificate }}
  mongodb-tls.crt: {{ .Values.database.certificate }}
  {{- end }}
