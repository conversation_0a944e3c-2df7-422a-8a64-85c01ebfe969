apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ .Values.global.namespace }}
  name: {{ include "vizivault-platform.fullname" (dict "root" . "app" .Values.cipher.name) }}
  labels:
    app.kubernetes.io/name: {{ .Values.cipher.name }}
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.cipher.name }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.cipher.name }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: {{ include "vizivault-platform.image" (dict "root" .Values "app" .Values.cipher.image ) }}
          imagePullPolicy: {{ .Values.cipher.image.pullPolicy }}
          {{- if .Values.containerSecurityContext }}
          securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: HOSTNAME
              value: 0.0.0.0
          ports:
            - name: grpc
              containerPort: 50051
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: grpc
          readinessProbe:
            tcpSocket:
              port: grpc
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      imagePullSecrets:
        - name: aws-ecr-secret
      {{- with .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- if .Values.podSecurityContext }}
      securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.podSecurityContext "context" $) | nindent 8 }}
    {{- end }}
