apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ .Values.global.namespace }}
  name: {{ include "vizivault-platform.fullname" (dict "root" . "app" .Values.api.name) }}
  labels:
    app.kubernetes.io/name: {{ .Values.api.name }}
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: {{ .Values.api.replicas }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.api.name }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.api.name }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      containers:
        - name: {{ printf "vizivault-%s" .Values.api.name }}
          image: {{ include "vizivault-platform.image" (dict "root" .Values "app" .Values.api.image ) }}
          imagePullPolicy: {{ .Values.api.image.pullPolicy }}
          {{- if .Values.containerSecurityContext }}
          securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            {{- toYaml .Values.api.resources | nindent 12 }}
          env:
            - name: SERVER_SERVLET_CONTEXTPATH
              value: {{ include "vizivault-platform.webContext" $ }}
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "vizivault-platform.databaseSecretName" . }}
                  key: mongodb-password
            - name: SPRING_DATA_MONGODB_URI
              value: {{ include "vizivault-platform.databaseURI" (dict "root" . "db" .Values.api.database) | quote}}
            - name: SPRING_DATA_MONGODB_DATABASE
              value: {{ .Values.api.database | quote }}
            - name: SPRING_RABBITMQ_HOST
              value: {{ include "vizivault-platform.rabbitHost" . | quote }}
            - name: SPRING_RABBITMQ_PORT
              value: {{ include "vizivault-platform.rabbitPort" . | quote }}
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: username
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: password
            - name: SPRING_RABBITMQ_VIRTUAL_HOST
              value: {{ include "vizivault-platform.rabbitVirtualHost" . | quote }}
            - name: SPRING_RABBITMQ_SSL_ENABLED
              value: {{ include "vizivault-platform.rabbitSSL" . | quote }}
            - name: NOX_API_AUTHORIZATION
              value: "true"
            - name: NOX_KEYS_EXCHANGE_TOKEN
              valueFrom:
                secretKeyRef:
                  name: vizivault-platform-secrets
                  key: vizivault-exchange-token
            - name: CIPHER_HOST
              value: vizivault-platform-cipher-service
            - name: CIPHER_PORT
              value: "50051"
            - name: ARBITER_HOST
              value: vizivault-platform-arbiter-service
            - name: ARBITER_PORT
              value: "50051"
        {{- if .Values.api.certificateRef }}
          volumeMounts:
            - name: ca-certs
              mountPath: /usr/local/openjdk-11/lib/security/cacerts
              subPath: cacerts
          {{- end }}
      {{- if .Values.vizivault.certificateRef }}
      volumes:
        - name: ca-certs
          configMap:
            name: {{ .Values.vizivault.certificateRef }}
            defaultMode: 420
      {{- end }}
      {{- if .Values.api.initializer }}
      initContainers:
        - name: initialize-api
          image: {{ include "vizivault-platform.image" (dict "root" .Values "app" .Values.initializer.image ) }}
          args:
            - scripts/initialize-api
          env:
            - name: MONGO_HOST
              value: {{ include "vizivault-platform.databaseHost" . | quote }}
            - name: MONGO_PORT
              value: {{ include "vizivault-platform.databasePort" . | quote }}
            - name: MONGO_USERNAME
              value: {{ include "vizivault-platform.databaseUser" . | quote }}
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "vizivault-platform.databaseSecretName" . }}
                  key: mongodb-password
            - name: MONGO_AUTH_SOURCE
              value: {{ include "vizivault-platform.authDb" . | quote }}
            - name: MONGO_SSL_ENABLED
              value: "false"
            - name: MONGO_NAMESPACE
              value: {{ .Values.vizivault.context | quote }}
            - name: RABBIT_HOST
              value: {{ include "vizivault-platform.rabbitHost" . | quote }}
            - name: RABBIT_PORT
              value: {{ include "vizivault-platform.rabbitPort" . | quote }}
      {{- end }}
      imagePullSecrets:
        - name: aws-ecr-secret
      {{- with .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.api.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.api.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- if .Values.podSecurityContext }}
      securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.podSecurityContext "context" $) | nindent 8 }}
    {{- end }}
