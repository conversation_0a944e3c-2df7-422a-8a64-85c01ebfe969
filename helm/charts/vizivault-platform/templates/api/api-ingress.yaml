{{- if .Values.ingress.api.enabled -}}
apiVersion: {{ template "common.capabilities.ingress.apiVersion" . }}
kind: Ingress
metadata:
  namespace: {{ .Values.global.namespace }}
  name: vizivault-platform-api-ingress
  labels:
    app.kubernetes.io/name: {{ .Release.Name }}
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  annotations:
    {{- if .Values.ingress.api.annotations  }}
    {{- include "common.tplvalues.render" (dict "value" .Values.ingress.api.annotations  "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" (dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
spec:
{{- with .Values.ingress.api }}
{{- if .tls.enabled }}
  tls:
    - hosts:
        - {{ .domain }}
      secretName: {{ .tls.secretName }}
{{- end }}
  rules:
    - host: {{ .domain }}
      http:
        paths:
          - path: {{ include "vizivault-platform.apiContext" $ }}
            pathType: Prefix
            backend:
              service:
                name: vizivault-platform-api-service
                port:
                  name: http
{{- end }}
{{- end }}
