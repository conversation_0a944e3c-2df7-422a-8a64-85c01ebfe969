apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ .Values.global.namespace }}
  name: {{ include "vizivault-platform.fullname" (dict "root" . "app" .Values.vizivault.name) }}
  labels:
    app.kubernetes.io/name: {{ .Values.vizivault.name }}
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.vizivault.name }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.vizivault.name }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      containers:
        - name: {{ printf "vizivault-%s" .Values.vizivault.name }}
          image: {{ include "vizivault-platform.image" (dict "root" .Values "app" .Values.vizivault.image ) }}
          imagePullPolicy: {{ .Values.vizivault.image.pullPolicy }}
          {{- if .Values.containerSecurityContext }}
          securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          resources:
            {{- toYaml .Values.vizivault.resources | nindent 12 }}
          env:
            - name: VIZIVAULT_ADMIN_USERNAME
              value: {{ .Values.vizivault.admin.username | quote }}
            - name: VIZIVAULT_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: vizivault-platform-secrets
                  key: vizivault-admin-password
            - name: VIZIVAULT_ADMIN_EMAIL
              value: {{ .Values.vizivault.admin.email | quote }}
            - name: VIZIVAULT_ADMIN_NAME
              value: {{ .Values.vizivault.admin.name | quote }}
            {{- include "vizivault-platform.authentication" . | nindent 12  }}
            - name: SERVER_SERVLET_CONTEXTPATH
              value: {{ include "vizivault-platform.webContext" $ }}
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "vizivault-platform.databaseSecretName" . }}
                  key: mongodb-password
            - name: SPRING_DATA_MONGODB_URI
              value: {{ include "vizivault-platform.databaseURI" (dict "root" . "db" .Values.vizivault.database) | quote}}
            - name: SPRING_DATA_MONGODB_DATABASE
              value: {{ .Values.vizivault.database | quote }}
            - name: SPRING_RABBITMQ_HOST
              value: {{ include "vizivault-platform.rabbitHost" . | quote }}
            - name: SPRING_RABBITMQ_PORT
              value: {{ include "vizivault-platform.rabbitPort" . | quote }}
            - name: SPRING_RABBITMQ_USERNAME
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: username
            - name: SPRING_RABBITMQ_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: rabbitmq-user-pass-secret
                  key: password
            - name: SPRING_RABBITMQ_VIRTUAL_HOST
              value: {{ include "vizivault-platform.rabbitVirtualHost" . | quote }}
            - name: SPRING_RABBITMQ_SSL_ENABLED
              value: {{ include "vizivault-platform.rabbitSSL" . | quote }}
            - name: NOVA_SERVICE_URI
              value: {{ include "vizivault-platform.appUrl" .  | quote }}
            - name: NOVA_BROADCAST_URI
              value: "http://vizivault-platform-alerts-service"
            - name: NOVA_SIGNING_KEY
              valueFrom:
                secretKeyRef:
                  name: vizivault-platform-secrets
                  key: token-signing-key
          {{- if .Values.vizivault.certificateRef }}
          volumeMounts:
            - name: ca-certs
              mountPath: /usr/local/openjdk-11/lib/security/cacerts
              subPath: cacerts
          {{- end }}
      {{- if .Values.vizivault.certificateRef }}
      volumes:
        - name: ca-certs
          configMap:
            name: {{ .Values.vizivault.certificateRef }}
            defaultMode: 420
      {{- end }}
      {{- if .Values.vizivault.initializer }}
      initContainers:
        - name: initialize-webapp
          image: {{ include "vizivault-platform.image" (dict "root" .Values "app" .Values.initializer.image ) }}
          args:
            - scripts/initialize-vizivault
          env:
            - name: MONGO_HOST
              value: {{ include "vizivault-platform.databaseHost" . | quote }}
            - name: MONGO_PORT
              value: {{ include "vizivault-platform.databasePort" . | quote }}
            - name: MONGO_USERNAME
              value: {{ include "vizivault-platform.databaseUser" . | quote }}
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "vizivault-platform.databaseSecretName" . }}
                  key: mongodb-password
            - name: MONGO_AUTH_SOURCE
              value: {{ include "vizivault-platform.authDb" . | quote }}
            - name: MONGO_SSL_ENABLED
              value: "false"
            - name: MONGO_NAMESPACE
              value: {{ .Values.vizivault.context | quote }}
            - name: RABBIT_HOST
              value: {{ include "vizivault-platform.rabbitHost" . | quote }}
            - name: RABBIT_PORT
              value: {{ include "vizivault-platform.rabbitPort" . | quote }}
      {{- end }}
      imagePullSecrets:
        - name: aws-ecr-secret
      {{- with .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.vizivault.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.vizivault.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- if .Values.podSecurityContext }}
      securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.podSecurityContext "context" $) | nindent 8 }}
    {{- end }}
