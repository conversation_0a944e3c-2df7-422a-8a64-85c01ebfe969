apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ .Values.global.namespace }}
  name: {{ include "vizivault-platform.fullname" (dict "root" . "app" .Values.arbiter.name) }}
  labels:
    app.kubernetes.io/name: {{ .Values.arbiter.name }}
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.arbiter.name }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.arbiter.name }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: {{ include "vizivault-platform.image" (dict "root" .Values "app" .Values.arbiter.image ) }}
          imagePullPolicy: {{ .Values.arbiter.image.pullPolicy }}
          {{- if .Values.containerSecurityContext }}
          securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          env:
            - name: HOSTNAME
              value: 0.0.0.0
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "vizivault-platform.databaseSecretName" . }}
                  key: mongodb-password
            - name: MONGO_URI
              value: {{ include "vizivault-platform.databaseURI" (dict "root" . "db" "") | quote}}
            - name: MONGO_AUTH_DB
              value: {{ .Values.arbiter.database | quote }}
          volumeMounts:
            - mountPath: /certs
              name: certs
          ports:
            - name: grpc
              containerPort: 50052
              protocol: TCP
          livenessProbe:
            tcpSocket:
              port: grpc
          readinessProbe:
            tcpSocket:
              port: grpc
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      initContainers:
        - name: initialize-arbiter
          image: {{ include "vizivault-platform.image" (dict "root" .Values "app" .Values.initializer.image ) }}
          args:
            - "wait-for-it"
            - "-h"
            - {{ include "vizivault-platform.databaseHost" . | quote }}
            - "-p"
            - {{ include "vizivault-platform.databasePort" . | quote }}
            - "-t"
            - "180"
          env:
            - name: MONGO_SSL_ENABLED
              value: "false"
      volumes:
        - name: certs
          configMap:
            defaultMode: 420
            name: vizivault-certs
      imagePullSecrets:
        - name: aws-ecr-secret
      {{- with .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- if .Values.podSecurityContext }}
      securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.podSecurityContext "context" $) | nindent 8 }}
    {{- end }}
