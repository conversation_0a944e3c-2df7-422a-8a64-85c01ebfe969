apiVersion: v1
kind: Service
metadata:
  namespace: {{ .Values.global.namespace }}
  name: vizivault-platform-arbiter-service
  labels:
    app.kubernetes.io/name: {{ .Release.Name }}
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.arbiter.service.type }}
  ports:
    - name: vizivault-arbiter
      port: 50051
      protocol: TCP
      targetPort: grpc
  selector:
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.arbiter.name }}
