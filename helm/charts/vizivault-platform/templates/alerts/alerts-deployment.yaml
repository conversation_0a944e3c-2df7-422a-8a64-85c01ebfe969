apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ .Values.global.namespace }}
  name: {{ include "vizivault-platform.fullname" (dict "root" . "app" .Values.alerts.name) }}
  labels:
    app.kubernetes.io/name: {{ .Values.alerts.name }}
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.alerts.name }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.alerts.name }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: {{ include "vizivault-platform.image" (dict "root" .Values "app" .Values.alerts.image ) }}
          imagePullPolicy: {{ .Values.alerts.image.pullPolicy }}
          {{- if .Values.containerSecurityContext }}
          securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.containerSecurityContext "context" $) | nindent 12 }}
          {{- end }}
          volumeMounts:
            - mountPath: "/usr/src/app/key"
              name: credentials
              readOnly: true
            - mountPath: /certs
              name: certs
          ports:
            - name: http
              containerPort: 3000
              protocol: TCP
            - name: ws
              containerPort: 3001
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /health
              port: http
          readinessProbe:
            httpGet:
              path: /health
              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: {{ include "vizivault-platform.databaseSecretName" . }}
                  key: mongodb-password
            - name: MONGO_URI
              value: {{ include "vizivault-platform.databaseURI" (dict "root" . "db" .Values.alerts.database) | quote}}
            {{- if .Values.database.certificate }}
            - name: MONGO_SSL_CA_PATH
              value: /certs/mongodb-tls.crt
            {{- end }}
            - name: SIGNING_KEY_PATH
              value: key/secret.key
      initContainers:
        - name: initialize-alerts
          image: {{ include "vizivault-platform.image" (dict "root" .Values "app" .Values.initializer.image ) }}
          args:
            - "wait-for-it"
            - "-h"
            - {{ include "vizivault-platform.databaseHost" . | quote }}
            - "-p"
            - {{ include "vizivault-platform.databasePort" . | quote }}
            - "-t"
            - "180"
      imagePullSecrets:
        - name: aws-ecr-secret
      volumes:
        - name: credentials
          secret:
            secretName: vizivault-platform-secrets
            items:
              - key: token-signing-key
                path: secret.key
        - name: certs
          configMap:
            defaultMode: 420
            name: vizivault-certs
      {{- with .Values.global.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- if .Values.podSecurityContext }}
      securityContext: {{- include "common.tplvalues.render" (dict "value" .Values.podSecurityContext "context" $) | nindent 8 }}
    {{- end }}
