apiVersion: v1
kind: Service
metadata:
  namespace: {{ .Values.global.namespace }}
  name: vizivault-platform-alerts-service
  labels:
    app.kubernetes.io/name: {{ .Release.Name }}
    helm.sh/chart: {{ include "vizivault-platform.chart" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.alerts.service.type }}
  ports:
    - name: vizivault-alerts-http
      port: 80
      protocol: TCP
      targetPort: http
    - name: vizivault-alerts-ws
      port: 81
      protocol: TCP
      targetPort: ws
  selector:
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/name: {{ printf "vizivault-%s" .Values.alerts.name }}
