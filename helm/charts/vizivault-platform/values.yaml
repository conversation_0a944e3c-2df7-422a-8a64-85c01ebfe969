global:
  registry: 514329541303.dkr.ecr.us-east-1.amazonaws.com
  imagePullSecrets:
    - name: aws-ecr-secret
  nodeSelector: {}
  namespace: vizivault

ingress:
  web:
    enabled: false
    domain: clerkxpress.com
    tls:
      enabled: true
      secretName: quickstart-example-tls
    annotations: |
      spec.ingressClassName: nginx
      cert-manager.io/issuer: "letsencrypt-prod"
      nginx.ingress.kubernetes.io/cors-allow-headers: Content-Type, authorization
      nginx.ingress.kubernetes.io/cors-allow-methods: PUT, GET, POST, OPTIONS
      nginx.ingress.kubernetes.io/cors-allow-origin: https://clerkxpress.com,http://clerkxpress.com
  api:
    enabled: false
    domain: clerkxpress.com
    tls:
      enabled: true
      secretName: quickstart-example-tls
    annotations: |
      spec.ingressClassName: nginx
      cert-manager.io/issuer: "letsencrypt-prod"
      nginx.ingress.kubernetes.io/cors-allow-headers: Content-Type, authorization
      nginx.ingress.kubernetes.io/cors-allow-methods: PUT, GET, POST, OPTIONS
      nginx.ingress.kubernetes.io/cors-allow-origin: https://clerkxpress.com,http://clerkxpress.com

# ViziVault Application
vizivault:
  name: "webapp"
  database: webapp-vizivault
  context: vizivault
  initializer: true
  certificateRef:
  image:
    repository: vizivault-webapp
    tag: 0.2.2-rc.3
    pullPolicy: IfNotPresent
  oauth:
    enabled: false
    provider:
    clientId:
    clientSecret:
    issuer:
    authorization:
    token:
    scope:
  admin:
    username: vizivault
    password: << replace me >>
    email: "<EMAIL>"
    name: "ViziVault Admin"
  service:
    type: ClusterIP

# Vault API
api:
  name: "api-vizivault"
  database: vault-vizivault
  context: vizivault-api
  replicas: 1
  initializer: true
  certificateRef:
  image:
    repository: vizivault-api
    tag: 0.1.0
    pullPolicy: IfNotPresent
  service:
    type: ClusterIP

# Notification Service
alerts:
  name: "alerts-vizivault"
  database: alerts-vizivault
  image:
    repository: vizivault-alerts
    tag: 0.1.0
    pullPolicy: IfNotPresent
  service:
    type: ClusterIP

# Cipher Encryption Service
cipher:
  name: "cipher-vizivault"
  image:
    repository: vizivault-cipher
    tag: 0.1.0
    pullPolicy: IfNotPresent
  service:
    type: ClusterIP

# Arbiter Validation Service
arbiter:
  name: "arbiter-vizivault"
  database: webapp-vizivault
  image:
    repository: vizivault-arbiter
    tag: 0.2.0
    pullPolicy: IfNotPresent
  service:
    type: ClusterIP

initializer:
  image:
    repository: vizivault-initializer
    tag: 0.2.2
    pullPolicy: IfNotPresent

# Database Configuration
# These values will be ignored if mongodb.enabled is set to true
database:
  authDb: admin
  username: vizivault-platform
  password: << replace me >>
  host: localhost
  port: 27017
  certificate:
  options: {}

# RabbitMQ Configuration
# These values will be ignored if rabbitmq.enabled is set to true
rabbit:
  username: << replace me >>
  password: << replace me >>
  host: rabbitmq.backend.svc.cluster.local
  port: 5672
  virtualHost: /
  useSSL: false

# MongoDB Configuration
# https://github.com/bitnami/charts/blob/master/bitnami/mongodb/values.yaml
mongodb:
  enabled: true
  global:
    namespaceOverride: vizivault
  architecture: standalone
  useStatefulSet: true
  service:
    port: 27017
  auth:
    enabled: true
    database: "admin"
    username: "vizivault-platform"
    password: "<< replace me >>"
  initdbScriptsConfigMap: vizivault-platform-initdb
  volumePermissions:
    enabled: true

# RabbitMQ Configuration
# https://github.com/bitnami/charts/blob/master/bitnami/rabbitmq/values.yaml
rabbitmq:
  enabled: false
  auth:
    username: vizivault-platform
  volumePermissions:
    enabled: true
