apiVersion: v2
appVersion: 0.1.0
dependencies:
  - name: common
    repository: https://charts.bitnami.com/bitnami
    tags:
      - bitnami-common
    version: 2.19.1
  - name: mongodb
    condition: mongodb.enabled
    repository: https://charts.bitnami.com/bitnami
    version: 12.1.16
  - name: rabbitmq
    condition: rabbitmq.enabled
    repository: https://charts.bitnami.com/bitnami
    version: 14.0.2
description: Chart for the ViziVault Platform
engine: gotpl
keywords:
  - vizivault
  - api
  - enterprise
  - platform
  - crypto
  - security
maintainers:
  - name: An<PERSON><PERSON>
    email: <EMAIL>
    url: https://anontech.io
name: vizivault-platform
version: 0.6.1
