# Running Keycloak Locally

1. Make sure to create `auth` database with `keycloak` schema.

    ```sql
    create database auth;

    \c auth

    create schema keycloak;
    ```

2. Make sure URL is correct in values.yaml:

    ```yaml
    - key: KC_DB_URL
      value: jdbc:postgresql://*************:5432/auth
    ```

    Should be one of these depending on how you run postgres:

    ```yaml
    - key: KC_DB_URL
      value: **************************************************************
    ```

    ```yaml
    - key: KC_DB_URL
      value: ************************************************
    ```

    ```yaml
    - key: KC_DB_URL
      value: *************************************
    ```

3. Go to [http://localhost://8080](http://localhost://8080) create user `admin` with your choice of a password.

4. <PERSON>gin go to create the `schenectady` realm and import `realm.json` export.

    - *Optional*: Go to realm settings, set email as username to off

5. Create a user for `schenectady` realm and add a password.

6. Create `auth-service` client.

	1. Selected `master` realm.

	2. General Settings --> Client Id --> `auth-service`.

    3. Capability Config --> Enable Client Authentication, de-select flow defaults and select service account.

    4. Assign role of admin.

7. Delete the `keycloak-admin` secret and the `keycloak-id-secrets` and regenerate from credentials in Keycloak.

8. Go to `auth-service` Service Account Roles tab and credentials and copy the token and add it to the `keycloak-admin` secret.

9. Do same for `clerkXpress-backend` and `keycloak-id` secrets.

10. Make sure to set local environment variable `KEYCLOAK_SERVER_TO_SERVER_AUTH_CLIENT_SECRET` to the clerkXpress-backend credential secret.
