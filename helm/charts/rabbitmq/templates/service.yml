apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.name }}
  namespace: {{ .Release.Namespace }}
  labels:
    group: {{ .Values.group }}
spec:
  type: {{ .Values.service.amqp.type }}
  selector:
    app: {{ .Values.name }}
  ports:
    - port: {{ .Values.service.amqp.port }}
      targetPort: {{ .Values.container.amqp.port }}
      name: {{ .Values.container.amqp.name }}
    - port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.container.http.port }}
      name: {{ .Values.container.http.name }}
