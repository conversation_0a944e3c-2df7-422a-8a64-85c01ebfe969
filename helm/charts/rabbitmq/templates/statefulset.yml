apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ .Values.name }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ .Values.name }}
    group: {{ .Values.group }}
spec:
  serviceName: {{ .Values.name }}
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app: {{ .Values.name }}
      group: {{ .Values.group }}
  template:
    metadata:
      labels:
        app: {{ .Values.name }}
        app.kubernetes.io/instance: {{ .Release.Name }}
        group: {{ .Values.group }}
    spec:
      volumes:
        - name: {{ .Values.volume.pvc.name }}
          persistentVolumeClaim:
            claimName: {{ .Values.volume.pvc.name }}
      imagePullSecrets:
        - name: aws-ecr-secret
      containers:
        - name: {{ .Values.name }}
          image: {{ .Values.container.image }}
          ports:
            - name: {{ .Values.container.amqp.name }}
              containerPort: {{ .Values.container.amqp.port }}
            - name: {{ .Values.container.http.name }}
              containerPort: {{ .Values.container.http.port }}
          envFrom:
            - configMapRef:
                name: {{ .Values.config.name }}
          imagePullPolicy: {{ .Values.config.imagePullPolicy }}
          resources:
            requests:
              cpu: {{ .Values.resources.requests.cpu }}
              memory: {{ .Values.resources.requests.memory }}
            limits:
              cpu: {{ .Values.resources.limits.cpu }}
              memory: {{ .Values.resources.limits.memory }}
          env:
            - name: {{ .Values.secrets.user.name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.user.secretName }}
                  key: {{ .Values.secrets.user.secretKey }}
            - name: {{ .Values.secrets.password.name }}
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.secrets.password.secretName }}
                  key: {{ .Values.secrets.password.secretKey }}
          volumeMounts:
            - name: {{ .Values.volume.pvc.name }}
              mountPath: {{ .Values.volume.mountPath }}
  volumeClaimTemplates:
  - metadata:
      name: {{ .Values.volume.pvc.name }}
    spec:
      accessModes:
      - {{ .Values.volume.pvc.accessMode }}
      resources:
        requests:
          storage: {{ .Values.volume.pvc.storage }}
