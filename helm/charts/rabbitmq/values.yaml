rabbitmq:
  name: rabbitmq
  group: messaging
  replicaCount: 1
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1024Mi"
      cpu: "500m"
  container:
    image: rabbitmq:3.11.9-management
    http:
      name: http
      port: 15672
    amqp:
      name: amqp
      port: 5672
  service:
    http:
      type: ClusterIP
      port: 15672
      name: http
    amqp:
      type: ClusterIP
      port: 5672
      name: amqp
  volume:
    name: rabbitmq-storage
    kind: PersistentVolumeClaim
    mountPath: /var/lib/rabbitmq/data
    pvc:
      name: rabbitmq-pvc
      kind: PersistentVolumeClaim
      accessMode: ReadWriteOnce
      storage: 10Gi
  config:
    name: rabbitmq-config
    imagePullPolicy: Always
    data:
      - key: management.path_prefix
        value: /rabbitmq/admin
      - key: log.console
        value: true
  secrets:
    user:
      name: RABBITMQ_USERNAME
      secretName: rabbitmq-user-pass-secret
      secretKey: username
    password:
      name: RABBITMQ_PASSWORD
      secretName: rabbitmq-user-pass-secret
      secretKey: password
  imagePullSecrets:
    - aws-ecr-secret
