rabbitmq:
  # Skaffold
  repo: rabbitmq
  tag: latest
  pullPolicy: Always
  # Application
  name: rabbitmq
  group: messaging
  replicaCount: 1
  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1024Mi"
      cpu: "500m"
  container:
    image: rabbitmq:3.11.9-management
    http:
      name: http
      port: 15672
    amqp:
      name: amqp
      port: 5672
  service:
    http:
      type: ClusterIP
      port: 15672
      name: http
    amqp:
      type: ClusterIP
      port: 5672
      name: amqp
  volume:
    name: rabbitmq-storage
    kind: PersistentVolumeClaim
    mountPath: /var/lib/rabbitmq/data
    pvc:
      name: rabbitmq-pvc
      kind: PersistentVolumeClaim
      accessMode: ReadWriteOnce
      storage: 2Gi
  config:
    name: rabbitmq-config
    imagePullPolicy: Always
    data:
      - key: management.path_prefix
        value: /rabbitmq/admin
      - key: log.console
        value: true
  secrets:
    user:
      name: RABBITMQ_USERNAME
      secretName: rabbitmq-user-pass-secret
      secretKey: username
    password:
      name: RABBITMQ_PASSWORD
      secretName: rabbitmq-user-pass-secret
      secretKey: password
  imagePullSecrets: aws-ecr-secret
keycloak:
  repo: keycloak
  tag: latest
  pullPolicy: Always
  replicaCount: 1
  name: keycloak
  namespace: keycloak
  group: auth
  args:
    - "start"
    - "--http-port=8080"
  container:
    image: 514329541303.dkr.ecr.us-east-1.amazonaws.com/scube-keycloak:25.0.1-3.0.3
    port: 8080
  deployment:
    readinessProbe: /realms/master
  service:
    name: keycloak-port
    type: ClusterIP
    port: 8080
  imagePullSecrets: aws-ecr-secret
  config:
    name: keycloak-config
    data:
      - key: KC_PROXY
        value: edge
      - key: KC_DB
        value: postgres
      - key: KC_DB_URL
        value: *****************************************
      - key: KC_DB_SCHEMA
        value: "keycloak"
      - key: KC_HOSTNAME_STRICT
        value: '"false"' # Renders wrong otherwise, must be Strings
      - key: PROXY_ADDRESS_FORWARDING
        value: '"false"' # Renders wrong otherwise, must be Strings
      - key: KEYCLOAK_FRONTEND_URL
        value: "https://clerkxpress.com/auth"
      - key: KEYCLOAK_ADMIN_URL
        value: "https://clerkxpress.com/auth/realms/master/admin/"
      - key: KK_TO_RMQ_URL
        value: "rabbitmq.backend.svc.cluster.local"
      - key: KK_TO_RMQ_VHOST
        value: "/"
      - key: KK_TO_RMQ_EXCHANGE
        value: "keycloak.events"
  secrets:
    rmqUsername:
      name: KK_TO_RMQ_USERNAME
      secretName: rabbitmq-user-pass-secret
      secretKey: username
    rmqPassword:
      name: KK_TO_RMQ_PASSWORD
      secretName: rabbitmq-user-pass-secret
      secretKey: password
    dbuser:
      name: KC_DB_USERNAME
      secretName: postgres-user-pass-secret
      secretKey: username
    dbpassword:
      name: KC_DB_PASSWORD
      secretName: postgres-user-pass-secret
      secretKey: password
    user:
      name: KEYCLOAK_USER
      secretName: keycloak-user-pass-secret
      secretKey: username
    password:
      name: KEYCLOAK_PASSWORD
      secretName: keycloak-user-pass-secret
      secretKey: password
vault:
  repo: vault
  tag: latest
  pullPolicy: Always
vizivault-platform:
  repo: vizivault-platform
  tag: latest
  pullPolicy: Always
