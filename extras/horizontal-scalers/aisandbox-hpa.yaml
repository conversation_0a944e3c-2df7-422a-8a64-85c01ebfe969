apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  namespace: ai-sandbox
  name: ai-sandbox-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-sandbox-api-depl
  minReplicas: 1
  maxReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageValue: 768Mi
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  namespace: ai-sandbox
  name: ai-sandbox-coordinator-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-sandbox-coordinator-depl
  minReplicas: 1
  maxReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageValue: 768Mi
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  namespace: ai-sandbox
  name: ai-sandbox-frontend-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ai-sandbox-frontend-depl
  minReplicas: 1
  maxReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageValue: 768Mi
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  namespace: ai-sandbox
  name: scube-document-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: scube-document-service-depl
  minReplicas: 1
  maxReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageValue: 768Mi
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  namespace: ai-sandbox
  name: service-langchain-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: service-langchain-depl
  minReplicas: 1
  maxReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageValue: 768Mi
