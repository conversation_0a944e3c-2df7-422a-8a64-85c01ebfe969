apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  namespace: frontend
  name: scube-backoffice-service-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: scube-backoffice-service-depl
  minReplicas: 1
  maxReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageValue: 768Mi
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  namespace: frontend
  name: scube-admin-portal-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: scube-admin-portal-depl
  minReplicas: 1
  maxReplicas: 2
  metrics:
    - type: Resource
      resource:
        name: memory
        target:
          type: Utilization
          averageValue: 768Mi
