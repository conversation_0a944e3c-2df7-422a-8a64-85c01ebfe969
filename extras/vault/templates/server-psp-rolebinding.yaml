{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{ template "vault.mode" . }}
{{- if .serverEnabled -}}
{{- if and (ne .mode "") (eq (.Values.global.psp.enable | toString) "true") }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ template "vault.fullname" . }}-psp
  namespace: hashicorp-vault
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
roleRef:
  kind: Role
  name: {{ template "vault.fullname" . }}-psp
  apiGroup: rbac.authorization.k8s.io
subjects:
  - kind: ServiceAccount
    name: {{ template "vault.fullname" . }}
{{- end }}
{{- end }}
