{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- template "vault.csiEnabled" . -}}
{{- if .csiEnabled -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ template "vault.fullname" . }}-csi-provider-role
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-csi-provider
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get"]
  resourceNames:
    {{- if .Values.csi.hmacSecretName }}
    - {{ .Values.csi.hmacSecretName }}
    {{- else }}
    - {{ include "vault.name" . }}-csi-provider-hmac-key
    {{- end }}
# 'create' permissions cannot be restricted by resource name:
# https://kubernetes.io/docs/reference/access-authn-authz/rbac/#referring-to-resources
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["create"]
{{- end }}
