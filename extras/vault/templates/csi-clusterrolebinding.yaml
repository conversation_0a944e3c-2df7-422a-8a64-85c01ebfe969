{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- template "vault.csiEnabled" . -}}
{{- if .csiEnabled -}}
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ template "vault.fullname" . }}-csi-provider-clusterrolebinding
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-csi-provider
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ template "vault.fullname" . }}-csi-provider-clusterrole
subjects:
- kind: ServiceAccount
  name: {{ template "vault.fullname" . }}-csi-provider
  namespace: hashicorp-vault
{{- end }}
