{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- if eq (.Values.server.networkPolicy.enabled | toString) "true"  }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: {{ template "vault.fullname" . }}
  labels:
    app.kubernetes.io/name: {{ template "vault.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
spec:
  podSelector:
    matchLabels:
      app.kubernetes.io/name: {{ template "vault.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  ingress:
    - from:
        - namespaceSelector: {}
      ports:
      - port: 8200
        protocol: TCP
      - port: 8201
        protocol: TCP
  {{- if .Values.server.networkPolicy.egress }}
  egress:
  {{- toYaml .Values.server.networkPolicy.egress | nindent 4 }}
  {{ end }}
{{ end }}
