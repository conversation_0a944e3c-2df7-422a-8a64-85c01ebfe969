{{/*
Copyright (c) HashiCorp, Inc.
SPDX-License-Identifier: MPL-2.0
*/}}

{{- template "vault.injectorEnabled" . -}}
{{- if .injectorEnabled -}}
# Deployment for the injector
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "vault.fullname" . }}-agent-injector
  namespace: hashicorp-vault
  labels:
    app.kubernetes.io/name: {{ include "vault.name" . }}-agent-injector
    app.kubernetes.io/instance: {{ .Release.Name }}
    app.kubernetes.io/managed-by: {{ .Release.Service }}
    component: webhook
spec:
  replicas: {{ .Values.injector.replicas }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ template "vault.name" . }}-agent-injector
      app.kubernetes.io/instance: {{ .Release.Name }}
      component: webhook
  {{ template "injector.strategy" . }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ template "vault.name" . }}-agent-injector
        app.kubernetes.io/instance: {{ .Release.Name }}
        component: webhook
        {{- if  .Values.injector.extraLabels -}}
          {{- toYaml .Values.injector.extraLabels | nindent 8 -}}
        {{- end -}}
      {{ template "injector.annotations" . }}
    spec:
      {{ template "injector.affinity" . }}
      {{ template "injector.topologySpreadConstraints" . }}
      {{ template "injector.tolerations" . }}
      {{ template "injector.nodeselector" . }}
      {{- if .Values.injector.priorityClassName }}
      priorityClassName: {{ .Values.injector.priorityClassName }}
      {{- end }}
      serviceAccountName: "{{ template "vault.fullname" . }}-agent-injector"
      {{ template "injector.securityContext.pod" . -}}
      {{- if not .Values.global.openshift }}
      hostNetwork: {{ .Values.injector.hostNetwork }}
      {{- end }}
      containers:
        - name: sidecar-injector
          {{ template "injector.resources" . }}
          image: "{{ .Values.injector.image.repository }}:{{ .Values.injector.image.tag }}"
          imagePullPolicy: "{{ .Values.injector.image.pullPolicy }}"
          {{- template "injector.securityContext.container" . }}
          env:
            - name: AGENT_INJECT_LISTEN
              value: {{ printf ":%v" .Values.injector.port  }}
            - name: AGENT_INJECT_LOG_LEVEL
              value: {{ .Values.injector.logLevel | default "info" }}
            - name: AGENT_INJECT_VAULT_ADDR
            {{- if .Values.global.externalVaultAddr }}
              value: "{{ .Values.global.externalVaultAddr }}"
            {{- else if .Values.injector.externalVaultAddr }}
              value: "{{ .Values.injector.externalVaultAddr }}"
            {{- else }}
              value: {{ include "vault.scheme" . }}://{{ template "vault.fullname" . }}.hashicorp-vault.svc:{{ .Values.server.service.port }}
            {{- end }}
            - name: AGENT_INJECT_VAULT_AUTH_PATH
              value: {{ .Values.injector.authPath }}
            - name: AGENT_INJECT_VAULT_IMAGE
              value: "{{ .Values.injector.agentImage.repository }}:{{ .Values.injector.agentImage.tag }}"
            {{- if .Values.injector.certs.secretName }}
            - name: AGENT_INJECT_TLS_CERT_FILE
              value: "/etc/webhook/certs/{{ .Values.injector.certs.certName }}"
            - name: AGENT_INJECT_TLS_KEY_FILE
              value: "/etc/webhook/certs/{{ .Values.injector.certs.keyName }}"
            {{- else }}
            - name: AGENT_INJECT_TLS_AUTO
              value: {{ template "vault.fullname" . }}-agent-injector-cfg
            - name: AGENT_INJECT_TLS_AUTO_HOSTS
              value: {{ template "vault.fullname" . }}-agent-injector-svc,{{ template "vault.fullname" . }}-agent-injector-svc.hashicorp-vault,{{ template "vault.fullname" . }}-agent-injector-svc.hashicorp-vault.svc
            {{- end }}
            - name: AGENT_INJECT_LOG_FORMAT
              value: {{ .Values.injector.logFormat | default "standard" }}
            - name: AGENT_INJECT_REVOKE_ON_SHUTDOWN
              value: "{{ .Values.injector.revokeOnShutdown | default false }}"
            {{- if .Values.global.openshift }}
            - name: AGENT_INJECT_SET_SECURITY_CONTEXT
              value: "false"
            {{- end }}
            {{- if .Values.injector.metrics.enabled }}
            - name: AGENT_INJECT_TELEMETRY_PATH
              value: "/metrics"
            {{- end }}
            {{- if and (eq (.Values.injector.leaderElector.enabled | toString) "true") (gt (.Values.injector.replicas | int) 1) }}
            - name: AGENT_INJECT_USE_LEADER_ELECTOR
              value: "true"
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            {{- end }}
            - name: AGENT_INJECT_CPU_REQUEST
              value: "{{ .Values.injector.agentDefaults.cpuRequest }}"
            - name: AGENT_INJECT_CPU_LIMIT
              value: "{{ .Values.injector.agentDefaults.cpuLimit }}"
            - name: AGENT_INJECT_MEM_REQUEST
              value: "{{ .Values.injector.agentDefaults.memRequest }}"
            - name: AGENT_INJECT_MEM_LIMIT
              value: "{{ .Values.injector.agentDefaults.memLimit }}"
            - name: AGENT_INJECT_DEFAULT_TEMPLATE
              value: "{{ .Values.injector.agentDefaults.template }}"
            - name: AGENT_INJECT_TEMPLATE_CONFIG_EXIT_ON_RETRY_FAILURE
              value: "{{ .Values.injector.agentDefaults.templateConfig.exitOnRetryFailure }}"
            {{- if .Values.injector.agentDefaults.templateConfig.staticSecretRenderInterval }}
            - name: AGENT_INJECT_TEMPLATE_STATIC_SECRET_RENDER_INTERVAL
              value: "{{ .Values.injector.agentDefaults.templateConfig.staticSecretRenderInterval }}"
            {{- end }}
            {{- include "vault.extraEnvironmentVars" .Values.injector | nindent 12 }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
          args:
            - agent-inject
            - 2>&1
          livenessProbe:
            httpGet:
              path: /health/ready
              port: {{ .Values.injector.port }}
              scheme: HTTPS
            failureThreshold: {{ .Values.injector.livenessProbe.failureThreshold }}
            initialDelaySeconds: {{ .Values.injector.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.injector.livenessProbe.periodSeconds }}
            successThreshold: {{ .Values.injector.livenessProbe.successThreshold }}
            timeoutSeconds: {{ .Values.injector.livenessProbe.timeoutSeconds }}
          readinessProbe:
            httpGet:
              path: /health/ready
              port: {{ .Values.injector.port }}
              scheme: HTTPS
            failureThreshold: {{ .Values.injector.readinessProbe.failureThreshold }}
            initialDelaySeconds: {{ .Values.injector.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.injector.readinessProbe.periodSeconds }}
            successThreshold: {{ .Values.injector.readinessProbe.successThreshold }}
            timeoutSeconds: {{ .Values.injector.readinessProbe.timeoutSeconds }}
          startupProbe:
            httpGet:
              path: /health/ready
              port: {{ .Values.injector.port }}
              scheme: HTTPS
            failureThreshold: {{ .Values.injector.startupProbe.failureThreshold }}
            initialDelaySeconds: {{ .Values.injector.startupProbe.initialDelaySeconds }}
            periodSeconds: {{ .Values.injector.startupProbe.periodSeconds }}
            successThreshold: {{ .Values.injector.startupProbe.successThreshold }}
            timeoutSeconds: {{ .Values.injector.startupProbe.timeoutSeconds }}
{{- if .Values.injector.certs.secretName }}
          volumeMounts:
            - name: webhook-certs
              mountPath: /etc/webhook/certs
              readOnly: true
{{- end }}
{{- if .Values.injector.certs.secretName }}
      volumes:
        - name: webhook-certs
          secret:
            secretName: "{{ .Values.injector.certs.secretName }}"
{{- end }}
      {{- include "imagePullSecrets" . | nindent 6 }}
{{ end }}
