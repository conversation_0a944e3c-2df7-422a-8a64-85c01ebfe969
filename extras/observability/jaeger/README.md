# Jaeger

- [J<PERSON><PERSON>](https://www.jaegertracing.io/)

1. Setup the Jaeger Operator:

```shell
kubectl create namespace observability

helm repo add jaegertracing https://jaegertracing.github.io/helm-charts
helm repo update
helm install jaeger-operator jaegertracing/jaeger-operator --namespace observability
```

2. Install the all-in-one image:

```shell
kubectl apply -f rbac.yaml
kubectl apply -f simple-deployment.yaml
```

### Upgrading

```shell
helm upgrade jaeger-operator jaegertracing/jaeger-operator --namespace observability
```
