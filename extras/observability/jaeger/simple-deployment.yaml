apiVersion: jaegertracing.io/v1
kind: Jaeger
metadata:
  name: clerkxpress-jaeger
  namespace: observability
spec:
  strategy: all-in-one
  allInOne:
    image: jaegertracing/all-in-one:latest
    options:
      log-level: info
    resources:
      limits:
        cpu: 500m
        memory: 512Mi
      requests:
        cpu: 256m
        memory: 128Mi
    securityContext:
      runAsUser: 10001
  collector:
    maxReplicas: 5
    resources:
      limits:
        cpu: 100m
        memory: 128Mi
  storage:
    type: badger
    options:
      badger:
        ephemeral: false
        directory-key: "/badger/key"
        directory-value: "/badger/data"
  volumeMounts:
    - name: data
      mountPath: /badger
  volumes:
    - name: data
      emptyDir: {}
  ui:
    options:
      menu:
        - label: "About Jaeger"
          items:
            - label: "Documentation"
              url: "https://www.jaegertracing.io/docs/latest"
