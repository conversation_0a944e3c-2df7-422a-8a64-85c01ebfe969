apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: jaeger-operator-ingressclass
rules:
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingressclasses"]
    verbs: ["list", "get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: jaeger-operator-ingressclass-binding
subjects:
  - kind: ServiceAccount
    name: jaeger-operator
    namespace: observability
roleRef:
  kind: ClusterRole
  name: jaeger-operator-ingressclass
  apiGroup: rbac.authorization.k8s.io
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: jaeger-operator-in-observability
  namespace: observability
subjects:
  - kind: ServiceAccount
    name: jaeger-operator
    namespace: observability
roleRef:
  kind: Role
  name: jaeger-operator
  apiGroup: rbac.authorization.k8s.io
