apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-nginx-frontend
  namespace: frontend
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Access-Control-Allow-Origin, X-Requested-With, X-Auth-Token, Content-Type, Content-Length, Authorization, Access-Control-Allow-Headers, Accept, X-Forwarded-For, X-Forwarded-Proto, X-Forwarded-Host"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS, PATCH"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://staging.clerkxpress.com, https://auth-staging.clerkxpress.com, https://cms-staging.clerkxpress.com, https://test.authorize.net"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "25k"
    nginx.ingress.kubernetes.io/large-client-header-buffers: "4 25k"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - staging.clerkxpress.com
      secretName: prod-tls
  rules:
    - host: staging.clerkxpress.com
      http:
        paths:
          - pathType: Prefix
            path: /
            backend:
              service:
                name: scube-backoffice-service-srv
                port:
                  name: backoffice-port
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-nginx-backend
  namespace: backend
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/proxy-body-size: "200m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Authorization, Content-Type, x-requested-with"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS, PATCH"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://staging.clerkxpress.com, https://auth-staging.clerkxpress.com"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "25k"
    nginx.ingress.kubernetes.io/large-client-header-buffers: "4 25k"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - staging.clerkxpress.com
      secretName: prod-tls
  rules:
    - host: staging.clerkxpress.com
      http:
        paths:
          - pathType: Prefix
            path: /api/auth
            backend:
              service:
                name: scube-auth-service-srv
                port:
                  name: auth-port
          - pathType: Prefix
            path: /api/calculation
            backend:
              service:
                name: scube-calculation-service-srv
                port:
                  name: calc-port
          - pathType: Prefix
            path: /api/config
            backend:
              service:
                name: scube-config-service-srv
                port:
                  name: config-port
          - pathType: Prefix
            path: /api/document-service
            backend:
              service:
                name: scube-document-service-srv
                port:
                  name: document-port
          - pathType: Prefix
            path: /api/license
            backend:
              service:
                name: scube-license-service-srv
                port:
                  name: license-port
          - pathType: Prefix
            path: /api/notification
            backend:
              service:
                name: scube-notification-service-srv
                port:
                  name: notify-port
          - pathType: Prefix
            path: /api/payment
            backend:
              service:
                name: scube-payment-service-srv
                port:
                  name: payment-port
          - pathType: Prefix
            path: /api/report
            backend:
              service:
                name: scube-report-service-srv
                port:
                  name: report-port
          - pathType: Prefix
            path: /api/ai
            backend:
              service:
                name: scube-ai-service-srv
                port:
                  name: ai-port
          - pathType: Prefix
            path: /api/document-template
            backend:
              service:
                name: scube-document-template-service-srv
                port:
                  name: template-port
          - pathType: Prefix
            path: /api/image-processing
            backend:
              service:
                name: scube-imageprocessing-service-srv
                port:
                  name: processing-port
          - pathType: Prefix
            path: /api/coordinator
            backend:
              service:
                name: scube-coordinator-service-srv
                port:
                  name: crdinator-port
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-nginx-keycloak
  namespace: keycloak
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "X-Requested-With, X-Auth-Token, Content-Type, Content-Length, Authorization, Access-Control-Allow-Headers, Accept, X-Forwarded-For, X-Forwarded-Proto, X-Forwarded-Host"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS, PATCH"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://staging.clerkxpress.com, https://auth-staging.clerkxpress.com"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "25k"
    nginx.ingress.kubernetes.io/large-client-header-buffers: "4 25k"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - auth-staging.clerkxpress.com
      secretName: prod-tls
  rules:
    - host: auth-staging.clerkxpress.com
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: keycloak
                port:
                  name: keycloak-port
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-nginx-vizivault
  namespace: vizivault
  annotations:
    cert-manager.io/issuer: "letsencrypt-prod"
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "X-Requested-With, X-Auth-Token, Content-Type, Content-Length, Authorization, Access-Control-Allow-Headers, Accept, X-Forwarded-For, X-Forwarded-Proto, X-Forwarded-Host"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS, PATCH"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://vizivault-staging.appliedtech.ai"
    nginx.ingress.kubernetes.io/app-root: "/vizivault"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - vizivault-staging.appliedtech.ai
      secretName: quickstart-example-tls
  rules:
    - host: vizivault-staging.appliedtech.ai
      http:
        paths:
          - pathType: Prefix
            path: "/"
            backend:
              service:
                name: vizivault-platform-web-service
                port:
                  number: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ingress-nginx-cms
  namespace: cms
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "Access-Control-Allow-Origin, X-Requested-With, X-Auth-Token, Content-Type, Content-Length, Authorization, Access-Control-Allow-Headers, Accept, X-Forwarded-For, X-Forwarded-Proto, X-Forwarded-Host"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, OPTIONS, PATCH"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://cms-staging.clerkxpress.com, https://staging.clerkxpress.com"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - cms-staging.clerkxpress.com
      secretName: quickstart-example-tls
  rules:
    - host: cms-staging.clerkxpress.com
      http:
        paths:
          - pathType: Prefix
            path: /
            backend:
              service:
                name: directus-service
                port:
                  name: directus-port
