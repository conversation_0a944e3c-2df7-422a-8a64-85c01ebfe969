# ingress-nginx

The base of the deployment file was retrieved from the official documentation. This is the version that's specifically intended for AWS deployments. Some modifications have been made to it to allow for CORS to function properly.

> **Note**: `ingress-nginx` and `nginx-ingress` are two different things. The naming is terrible so it's easy to get them mixed up. We are using **ingress-nginx**.

You can view the documentation [here](https://kubernetes.github.io/ingress-nginx/deploy/#aws).

## Deploying

> **Note**: Ingress isn't needed locally, port forwarding and using localhost is less of a hassle for development.

Just apply the ingress-nginx deploy at the target context. After that is running, give the cluster the Ingress object definitions and routing should be available shortly after.

```sh
kubectl apply -f deployment.yaml
```

```sh
kubectl apply -f ingress.yaml
```

## Links

- [Main GitHub repository](https://github.com/kubernetes/ingress-nginx)
- [Main Documentation](https://kubernetes.github.io/ingress-nginx/)
- [AWS Deployment Information from the source](https://kubernetes.github.io/ingress-nginx/deploy/#aws)

# Cert Manager

Required for getting valid TLS certificates signed by an authority, it handles automatically renewing them when they expire or are deleted for whatever reason. The YAML installs multiple custom Kuberentes resources that you can view to see the stages of certificate procurement like `order` and `issuer`.

Installation involves just running the usual `kubectl` commands:

```sh
kubectl apply -f cert-manager.yaml
kubectl apply -f issuer-production.yaml
```

You can install the `issuer-testing.yaml` instead to work against a test issuer to work out kinks without hitting rate limits from the actual certificate authority. This will not function in productioin and should be unisntall after you're done.

The default TLS secrets are named `quickstart-example-tls` (I didn't bother renaming the name from the demo application, it doesn't matter). This secret needs to be referred to in the Ingress definitions so that nginx and terminate the TLS.

```yaml
...
tls:
  - hosts:
      - clerkxpress.com
    secretName: quickstart-example-tls
...
```

## Links

- [cert-manager](https://cert-manager.io/)
