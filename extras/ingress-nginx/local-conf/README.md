# Local NGINX

Tired of remembering port numbers? It's possible to run nginx locally using this configuration setup to match a locally running stack with their ports forwarded.

## Steps

1. Download nginx via your choice of package manager or direct from their [website](https://nginx.org/en/download.html).
2. in the `/conf` directory, replace it with the `nginx.conf` here in the directory.
3. Run nginx
    - You'll need to run the executable as an administrator if you're on Windows

### Start nginx on Startup in Windows

See official documentation [here](https://support.microsoft.com/en-us/windows/add-an-app-to-run-automatically-at-startup-in-windows-10-150da165-dcd9-7230-517b-cf3c295d89dd). You'll want to updated the executable to check the "Run as administrator" if you desire this rotue.
