worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    client_max_body_size 300M;
    large_client_header_buffers 4 20k;

    server {
        listen 3030;
        server_name localhost;

        location /rabbit/ {
            rewrite ^/rabbit(.*)$ $1 break;
            proxy_pass http://localhost:15672;
            proxy_set_header Host localhost:3030;
        }

        location / {
            proxy_pass http://localhost:8080;
            proxy_set_header Host localhost:3030;
        }

        location /api/license/ {
            proxy_pass http://localhost:9004;
            proxy_set_header Host localhost:3030;
        }

        location /api/auth/ {
            proxy_pass http://localhost:9001;
            proxy_set_header Host localhost:3030;
        }

        location /api/calculation/ {
            proxy_pass http://localhost:9002;
            proxy_set_header Host localhost:3030;
        }

        location /api/document-service/ {
            proxy_pass http://localhost:9003;
            proxy_set_header Host localhost:3030;
        }

        location /api/notification/ {
            proxy_pass http://localhost:9005;
            proxy_set_header Host localhost:3030;
        }

        location /api/payment/ {
            proxy_pass http://localhost:9006;
            proxy_set_header Host localhost:3030;
        }

        location /api/report/ {
            proxy_pass http://localhost:9007;
            proxy_set_header Host localhost:3030;
        }

        location /api/ocr/ {
            proxy_pass http://localhost:9008;
            proxy_set_header Host localhost:3030;
        }

        location /api/document-template/ {
            proxy_pass http://localhost:9009;
            proxy_set_header Host localhost:3030;
        }

        location /api/image-processing/ {
            proxy_pass http://localhost:9010;
            proxy_set_header Host localhost:3030;
        }

        location /api/ai/ {
            proxy_pass http://localhost:9011;
            proxy_set_header Host localhost:3030;
        }

        location /api/document-template-helper/ {
            proxy_pass http://localhost:9012;
            proxy_set_header Host localhost:3030;
        }

        location /api/config/ {
            proxy_pass http://localhost:10001;
            proxy_set_header Host localhost:3030;
        }

        location /api/coordinator/ {
            proxy_pass http://localhost:10004;
            proxy_set_header Host localhost:3030;
        }
    }
}
