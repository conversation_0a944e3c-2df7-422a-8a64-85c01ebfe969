# Directus

Directus is a headless CMS that services data to the frontend. It allows non-developer users to update information on the site as-needed.

## Runnning Locally

Should you want to take a look at Directus yourself, you can run it via Docker quickly.

```shell
# From their quickstart
docker run -p 8055:8055 -e KEY=replace-with-random-value -e SECRET=replace-with-random-value directus/directus:10.11.1
```

```shell
# Running against a PostgreSQL database
docker run --name directus -p 8055:8055 -e KEY=replace-with-random-value -e SECRET=replace-with-random-value -e ADMIN_EMAIL=initial-admin-email-address -e ADMIN_PASSWORD=initial-admin-password -e DB_CLIENT=pg -e DB_CONNECTION_STRING="***************************************************/directus?sslmode=disable" directus/directus:10.11.1
```

## Links

- [Directus](https://directus.io/)
- [Directus Documentation](https://docs.directus.io/)
