apiVersion: apps/v1
kind: StatefulSet
metadata:
  namespace: cms
  name: directus-statefulset
spec:
  serviceName: directus
  replicas: 1
  selector:
    matchLabels:
      app: directus
  template:
    metadata:
      labels:
        app: directus
    spec:
      containers:
        - name: directus
          image: directus/directus:10.12.0
          livenessProbe:
            initialDelaySeconds: 60
            periodSeconds: 30
            tcpSocket:
              port: 8055
          readinessProbe:
            initialDelaySeconds: 30
            periodSeconds: 30
            tcpSocket:
              port: 8055
          ports:
            - containerPort: 8055
          env:
            - name: KEY
              valueFrom:
                secretKeyRef:
                  name: directus-config
                  key: key
            - name: SECRET
              valueFrom:
                secretKeyRef:
                  name: directus-config
                  key: secret
            - name: ADMIN_EMAIL
              valueFrom:
                secretKeyRef:
                  name: directus-config
                  key: admin-email
            - name: ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: directus-config
                  key: admin-password
            - name: DB_CLIENT
              value: pg
            - name: DB_CONNECTION_STRING
              valueFrom:
                secretKeyRef:
                  name: directus-postgres
                  key: connection-string
            - name: STORAGE_LOCATIONS
              value: "s3"
            - name: STORAGE_S3_DRIVER
              value: "s3"
            - name: STORAGE_S3_KEY
              valueFrom:
                secretKeyRef:
                  name: aws-user-pass-secret
                  key: aws-access-key
            - name: STORAGE_S3_SECRET
              valueFrom:
                secretKeyRef:
                  name: aws-user-pass-secret
                  key: aws-secret-key
            - name: STORAGE_S3_REGION
              value: "us-east-1"
            - name: STORAGE_S3_BUCKET
              value: "clerkxpress-directus-staging"
            - name: STORAGE_S3_FOLDER
              value: "directus"
            - name: STORAGE_S3_ENDPOINT
              value: "s3.us-east-1.amazonaws.com"
          resources:
            limits:
              cpu: "250m"
              memory: "512Mi"
          volumeMounts:
            - name: directus-data
              mountPath: /var/directus
      volumes:
        - name: directus-data
          persistentVolumeClaim:
            claimName: directus-pvc
  volumeClaimTemplates:
    - metadata:
        name: directus-data
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 10Gi
---
apiVersion: v1
kind: Service
metadata:
  name: directus-service
  namespace: cms
spec:
  selector:
    app: directus
  ports:
    - name: directus-port
      protocol: TCP
      port: 8055
      targetPort: 8055
---
apiVersion: cert-manager.io/v1
kind: Issuer
metadata:
  name: letsencrypt-prod
  namespace: cms
spec:
  acme:
    # The ACME server URL
    server: https://acme-v02.api.letsencrypt.org/directory
    # Email address used for ACME registration
    email: <EMAIL>
    # Name of a secret used to store the ACME account private key
    privateKeySecretRef:
      name: letsencrypt-production-private-key
    # Enable the HTTP-01 challenge provider
    solvers:
      - http01:
          ingress:
            ingressClassName: nginx
