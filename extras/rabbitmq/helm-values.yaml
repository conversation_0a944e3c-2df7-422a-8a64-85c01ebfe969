vhosts:
  - "/"
  - "chazy"
  - "demo"
  - "greenwich"
  - "haddam"
  - "nysliquorauthority"
  - "schenectady"
auth:
  username: guest
  password: guest
resources:
  limits:
    memory: "2Gi"
    cpu: "1000m"
  requests:
    memory: "1Gi"
    cpu: "500m"
extraSecrets:
  load-definition:
    load_definition.json: |
      {
        "users": [
          {
            "name": "{{ .Values.auth.username }}",
            "password": "{{ .Values.auth.password }}",
            "tags": "administrator"
          }
        ],
        "vhosts": [
          {{- range $index, $vhost := .Values.vhosts }}
          {
            "name": "{{ $vhost }}"
          }{{ if not (eq $index (sub (len $.Values.vhosts) 1)) }},{{ end }}
          {{- end }}
        ],
        "permissions": [
          {{- range $index, $vhost := .Values.vhosts }}
          {
            "user": "{{ $.Values.auth.username }}",
            "vhost": "{{ $vhost }}",
            "configure": ".*",
            "write": ".*",
            "read": ".*"
          }{{ if not (eq $index (sub (len $.Values.vhosts) 1)) }},{{ end }}
          {{- end }}
        ]
      }
loadDefinition:
  enabled: true
  existingSecret: load-definition
extraConfiguration: |
  load_definitions = /app/load_definition.json
