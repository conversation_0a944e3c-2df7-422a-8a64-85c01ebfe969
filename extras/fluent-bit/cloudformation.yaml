AWSTemplateFormatVersion: '2010-09-09'
Description: 'CloudWatch Alarm for EKS Namespace-based Error Monitoring'

Parameters:
  ClusterName:
    Type: String
    Default: "Licensing-Dev"
    Description: "EKS Cluster Name"

  NotificationEmail:
    Type: String
    Description: "Email address to receive error notifications"
    Default: "<EMAIL>"

  ErrorThreshold:
    Type: Number
    Default: 5
    Description: "Number of errors in the evaluation period to trigger alarm"

  EvaluationPeriods:
    Type: Number
    Default: 2
    Description: "Number of periods to evaluate"

  Period:
    Type: Number
    Default: 300
    Description: "Period in seconds (300 = 5 minutes)"

  MonitoredNamespaces:
    Type: CommaDelimitedList
    Default: "backend,s-and-f"
    Description: "Comma-separated list of namespaces to monitor"

Resources:
  # SNS Topic for notifications
  # ErrorNotificationTopic:
  #   Type: AWS::SNS::Topic
  #   Properties:
  #     TopicName: !Sub "${ClusterName}-error-notifications"
  #     DisplayName: !Sub "${ClusterName} Namespace Error Notifications"

  # # SNS Subscription
  # ErrorNotificationSubscription:
  #   Type: AWS::SNS::Subscription
  #   Properties:
  #     Protocol: email
  #     TopicArn: !Ref ErrorNotificationTopic
  #     Endpoint: !Ref NotificationEmail

  # Metric Filter for Backend namespace errors
  BackendNamespaceErrorMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub "/aws/eks/${ClusterName}/errors"
      FilterPattern: '"ERROR" "backend"'
      MetricTransformations:
        - MetricNamespace: !Sub "EKS/${ClusterName}/Namespaces"
          MetricName: "BackendErrors"
          MetricValue: "1"
          DefaultValue: 0

  # Metric Filter for S-and-F namespace errors
  SandFNamespaceErrorMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub "/aws/eks/${ClusterName}/errors"
      FilterPattern: '"ERROR" "s-and-f"'
      MetricTransformations:
        - MetricNamespace: !Sub "EKS/${ClusterName}/Namespaces"
          MetricName: "SandFErrors"
          MetricValue: "1"
          DefaultValue: 0

  # Overall error count across all namespaces
  OverallErrorMetricFilter:
    Type: AWS::Logs::MetricFilter
    Properties:
      LogGroupName: !Sub "/aws/eks/${ClusterName}/errors"
      FilterPattern: '"ERROR"'
      MetricTransformations:
        - MetricNamespace: !Sub "EKS/${ClusterName}"
          MetricName: "TotalErrorCount"
          MetricValue: "1"
          DefaultValue: 0

  # Overall Error Count Alarm
  OverallErrorCountAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ClusterName}-high-error-rate"
      AlarmDescription: !Sub "High error rate detected across ${ClusterName} cluster"
      MetricName: TotalErrorCount
      Namespace: !Sub "EKS/${ClusterName}"
      Statistic: Sum
      Period: !Ref Period
      EvaluationPeriods: !Ref EvaluationPeriods
      Threshold: !Ref ErrorThreshold
      ComparisonOperator: GreaterThanOrEqualToThreshold
      #AlarmActions:
      #  - !Ref ErrorNotificationTopic
      #OKActions:
      #  - !Ref ErrorNotificationTopic
      TreatMissingData: notBreaching

  # Backend Namespace Error Alarm
  BackendNamespaceErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ClusterName}-backend-namespace-errors"
      AlarmDescription: "Errors detected in Backend namespace"
      MetricName: BackendErrors
      Namespace: !Sub "EKS/${ClusterName}/Namespaces"
      Statistic: Sum
      Period: !Ref Period
      EvaluationPeriods: 2
      DatapointsToAlarm: 2
      Threshold: 3
      ComparisonOperator: GreaterThanOrEqualToThreshold
      #AlarmActions:
      #  - !Ref ErrorNotificationTopic
      #OKActions:
      #  - !Ref ErrorNotificationTopic
      TreatMissingData: notBreaching

  # S-and-F Namespace Error Alarm
  SandFNamespaceErrorAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ClusterName}-s-and-f-namespace-errors"
      AlarmDescription: "Errors detected in S-and-F namespace"
      MetricName: SandFErrors
      Namespace: !Sub "EKS/${ClusterName}/Namespaces"
      Statistic: Sum
      Period: !Ref Period
      EvaluationPeriods: 2
      DatapointsToAlarm: 2
      Threshold: 3
      ComparisonOperator: GreaterThanOrEqualToThreshold
      #AlarmActions:
      #  - !Ref ErrorNotificationTopic
      #OKActions:
      #  - !Ref ErrorNotificationTopic
      TreatMissingData: notBreaching

  # Dashboard for namespace-based monitoring
  NamespaceErrorMonitoringDashboard:
    Type: AWS::CloudWatch::Dashboard
    Properties:
      DashboardName: !Sub "${ClusterName}-namespace-error-monitoring"
      DashboardBody: !Sub |
        {
          "widgets": [
            {
              "type": "metric",
              "x": 0,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "EKS/${ClusterName}", "TotalErrorCount" ]
                ],
                "period": 300,
                "stat": "Sum",
                "region": "${AWS::Region}",
                "title": "Total Error Count (All Namespaces)",
                "yAxis": {
                  "left": {
                    "min": 0
                  }
                }
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "EKS/${ClusterName}/Namespaces", "BackendErrors" ],
                  [ ".", "SandFErrors" ]
                ],
                "period": 300,
                "stat": "Sum",
                "region": "${AWS::Region}",
                "title": "Errors by Namespace",
                "yAxis": {
                  "left": {
                    "min": 0
                  }
                }
              }
            },
            {
              "type": "log",
              "x": 0,
              "y": 6,
              "width": 24,
              "height": 6,
              "properties": {
                "query": "SOURCE '/aws/eks/${ClusterName}/errors' | fields @timestamp, kubernetes.namespace_name as namespace, kubernetes.pod_name as pod, message\n| filter @message like /ERROR/\n| sort @timestamp desc\n| limit 100",
                "region": "${AWS::Region}",
                "title": "Recent Error Logs by Namespace",
                "view": "table"
              }
            },
            {
              "type": "metric",
              "x": 0,
              "y": 12,
              "width": 24,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "EKS/${ClusterName}/Namespaces", "BackendErrors" ],
                  [ ".", "SandFErrors" ]
                ],
                "view": "stacked",
                "stacked": true,
                "period": 300,
                "stat": "Sum",
                "region": "${AWS::Region}",
                "title": "Error Trends by Namespace (Stacked)",
                "yAxis": {
                  "left": {
                    "min": 0
                  }
                }
              }
            }
          ]
        }

Outputs:
  # SNSTopicArn:
  #   Description: "ARN of the SNS topic for error notifications"
  #   Value: !Ref ErrorNotificationTopic

  DashboardURL:
    Description: "URL to the CloudWatch Dashboard"
    Value: !Sub "https://${AWS::Region}.console.aws.amazon.com/cloudwatch/home?region=${AWS::Region}#dashboards:name=${ClusterName}-namespace-error-monitoring"

  MonitoredNamespaces:
    Description: "List of namespaces being monitored"
    Value: !Join [", ", !Ref MonitoredNamespaces]
