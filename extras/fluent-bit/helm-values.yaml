# Fluent Bit Helm Chart Values for Spring Boot EKS Monitoring

# Environment variables
env:
  - name: NAMESPACE
    value: "amazon-cloudwatch"
  - name: AWS_REGION
    value: "us-east-1"
  - name: CLUSTER_NAME
    value: "Licensing-Dev"

# Service Account - use default (relies on node instance profile)
serviceAccount:
  create: true
  name: fluent-bit
  annotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/fluent-bit-role"

# Resource limits
resources:
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 100m
    memory: 128Mi

# Tolerations to run on all nodes
tolerations:
  - key: node-role.kubernetes.io/master
    operator: Exists
    effect: NoSchedule
  - operator: "Exists"
    effect: "NoExecute"
  - operator: "Exists"
    effect: "NoSchedule"

# Custom configuration
config:
  service: |
    [SERVICE]
        Daemon Off
        Flush {{ .Values.flush }}
        Log_Level {{ .Values.logLevel }}
        Parsers_File parsers.conf
        Parsers_File custom_parsers.conf
        HTTP_Server On
        HTTP_Listen 0.0.0.0
        HTTP_Port {{ .Values.metricsPort }}
        Health_Check On

  inputs: |
    [INPUT]
        Name tail
        Path /var/log/containers/*.log
        multiline.parser docker, cri
        Tag kube.*
        Mem_Buf_Limit 50MB
        Skip_Long_Lines On
        Skip_Empty_Lines On

  filters: |
    [FILTER]
        Name kubernetes
        Match kube.*
        Kube_URL https://kubernetes.default.svc:443
        Kube_CA_File /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File /var/run/secrets/kubernetes.io/serviceaccount/token
        Kube_Tag_Prefix kube.var.log.containers.
        Merge_Log On
        Keep_Log Off
        K8S-Logging.Parser On
        K8S-Logging.Exclude Off
        Annotations Off
        Labels On

    # Parse Spring Boot logs
    [FILTER]
        Name parser
        Match kube.*
        Key_Name log
        Parser spring_boot_parser
        Reserve_Data On
        Preserve_Key On

    # Add cluster information
    [FILTER]
        Name modify
        Match kube.*
        Add cluster ${CLUSTER_NAME}

    # Create separate stream for ERROR logs
    [FILTER]
        Name rewrite_tag
        Match kube.*
        Rule $level ^ERROR$ error_logs false

  outputs: |
    [OUTPUT]
        Name cloudwatch_logs
        Match error_logs
        region ${AWS_REGION}
        log_group_name /aws/eks/${CLUSTER_NAME}/errors
        log_stream_prefix spring_boot_
        auto_create_group On
        retry_limit 2

  customParsers: |
    [PARSER]
        Name spring_boot_parser
        Format regex
        Regex ^(?<timestamp>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}[+-]\d{2}:\d{2})\s+(?<level>\w+)\s+\[tenantId=(?<tenant>[^\]]*)\],\s+\[(?<trace_id>[^,]*),?(?<span_id>[^\]]*)\]\s+\[user=(?<user>[^\]]*)\]\s+(?<pid>\d+)\s+---\s+\[(?<service>[^\]]+)\]\s+\[(?<thread>[^\]]+)\]\s+(?<logger>[^\s]+)\s*:\s+(?<message>.*)$
        Time_Key timestamp
        Time_Format %Y-%m-%dT%H:%M:%S.%L%z
        Time_Keep On

    [PARSER]
        Name tenant_extractor
        Format regex
        Regex ^.*\[tenantId=(?<tenant_id>[^\]]*)\].*$

    [PARSER]
        Name service_extractor
        Format regex
        Regex ^.*\[(?<service_name>[^\]]+)\].*\[(?<thread_name>[^\]]+)\].*$

# Use AWS-optimized Fluent Bit image
image:
  repository: public.ecr.aws/aws-observability/aws-for-fluent-bit
  tag: "stable"
  pullPolicy: Always

# Monitoring
serviceMonitor:
  enabled: true
  namespace: monitoring
  interval: 30s
  scrapeTimeout: 10s

# Log level
logLevel: info
flush: 5
metricsPort: 2020
