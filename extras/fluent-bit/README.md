# Fluent Bit

This service is utilized to funnel Kubernentes logs out of the cluster and into a more relevant location. In our current setup, that location is AWS CloudWatch. No changes are needed to have this serviece work as we're doing absolutely nothing special with the logging setup. Logs will automatically start appearing in AWS for metrics after a bit of time.

```powershell
helm repo add fluent https://fluent.github.io/helm-charts
helm repo update

# Installing
helm install fluent-bit fluent/fluent-bit `
    --namespace amazon-cloudwatch `
    --values helm-values.yaml

# Upgrading existing
helm upgrade fluent-bit fluent/fluent-bit `
    --namespace amazon-cloudwatch `
    --values helm-values.yaml
```

## Setup

```powershell
# Get the OIDC issuer for the cluster you're working on
aws eks describe-cluster --name {{your-cluster-name}} --query "cluster.identity.oidc.issuer" --output text

# Add any to the trust-policy.json
aws iam create-role `
    --role-name fluent-bit-role `
    --assume-role-policy-document file://trust-policy.json

# If, for some reason, the fluent-bit-role is not longer attached to the policy
aws iam attach-role-policy `
    --role-name fluent-bit-role `
    --policy-arn arn:aws:iam::514329541303:policy/FluentBitCloudWatchPolicy
```

## Cloud Formation

This will handle creating the SNS, Alarms and emails for getting alerted by the error logs.

```powershell
aws cloudformation create-stack `
  --stack-name licensing-dev-error-monitoring `
  --template-body file://cloudformation.yaml `
  --parameters ParameterKey=NotificationEmail,ParameterValue=<EMAIL> `
               ParameterKey=ErrorThreshold,ParameterValue=5 `
  --region us-east-1

  aws cloudformation update-stack `
  --stack-name licensing-dev-error-monitoring `
  --template-body file://cloudformation.yaml `
  --parameters ParameterKey=NotificationEmail,ParameterValue=<EMAIL> `
               ParameterKey=ErrorThreshold,ParameterValue=5 `
  --region us-east-1
```

## Links

- [Fluent Bit](https://docs.fluentbit.io/manual)
- [AWS Cloudwatch](https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/WhatIsCloudWatch.html)
