# Change contexts to never screw with the AWS EKS clusters
kubectl config use-context docker-desktop

# Make any namespaces needed
kubectl create namespace backend
kubectl create namespace frontend
kubectl create namespace keycloak
kubectl create namespace ai-sandbox
kubectl create namespace hashicorp-vault
kubectl create namespace vizivault

# Log into ECR for image pulling
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 514329541303.dkr.ecr.us-east-1.amazonaws.com

# Run stack via Skaffold
skaffold dev -p local -n backend --port-forward --skip-tests
