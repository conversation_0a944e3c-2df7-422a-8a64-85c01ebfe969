@REM Script for creating the deployments
@echo off

set DEV_OUTPUT=deployments/development.yaml
set STAGING_OUTPUT=deployments/staging.yaml
set PRODUCTION_OUTPUT=deployments/production.yaml

echo Rendering the development yaml...
skaffold render -d 514329541303.dkr.ecr.us-east-1.amazonaws.com --output %DEV_OUTPUT% -n backend -p aws-development -t latest
echo Development available at %DEV_OUTPUT%
echo ----------------------------------------------------

echo Rendering the staging yaml...
skaffold render -d 514329541303.dkr.ecr.us-east-1.amazonaws.com --output %STAGING_OUTPUT% -n backend -p aws-staging -t staging
echo Staging available at %STAGING_OUTPUT%
echo ----------------------------------------------------

echo Rendering the production yaml...
skaffold render -d 514329541303.dkr.ecr.us-east-1.amazonaws.com --output %PRODUCTION_OUTPUT% -n backend -p aws-production -t latest
echo Production available at %PRODUCTION_OUTPUT%
echo ----------------------------------------------------
