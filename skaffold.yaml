# A Skaffold File for endering the entire stack.
#
# skaffold render -d 514329541303.dkr.ecr.us-east-1.amazonaws.com --output deployment.yaml -n backend -p aws-development -t latest

apiVersion: skaffold/v4beta2
kind: Config
metadata:
  name: backend
build:
  tagPolicy:
    sha256: {}
  artifacts:
    - image: service_auth
      context: ../Service_Auth
      jib:
        project: com.scube.auth:auth-application
    - image: service_calculation
      context: ../Service_Calculation
      jib:
        project: com.scube.calculation:calculation-application
    - image: service_config
      context: ../Service_Config
      jib:
        project: com.scube.config:config-application
    - image: service_document
      context: ../Service_Document
      jib:
        project: com.scube.document:document-application
    - image: service_license
      context: ../Service_License
      jib:
        project: com.scube.licensing:license-application
    - image: service_notification
      context: ../Service_Notification
      jib:
        project: com.scube.notification:notification-application
    - image: service_report
      context: ../Service_Report
      jib:
        project: com.scube.report:report-application
    - image: service_payment
      context: ../Service_Payment
      jib:
        project: com.scube.payment:payment-application
    - image: service_document_template
      context: ../Service_DocumentTemplates
      jib:
        project: com.scube.documenttemplates:documenttemplates-application
    - image: service_document_template_helper
      context: ../Service_Document_Template_Helper
      docker:
        dockerfile: Dockerfile
    - image: service_ocr
      context: ../Service_OCR/
      docker:
        dockerfile: Dockerfile
    - image: service_ai
      context: ../Service_AI
      jib:
        project: com.scube.ai:ai-application
    - image: service_imageprocessing
      context: ../Service_ImageProcessing
      jib:
        project: com.scube.imageprocessing:imageprocessing-application
    - image: service_coordinator
      context: ../Service_Coordinator
      jib:
        project: com.scube.coordinator:coordinator-application
manifests:
  rawYaml:
    - ../Service_AI/deployment.yml
    - ../Service_Auth/deployment.yml
    - ../Service_Calculation/deployment.yml
    - ../Service_Config/deployment.yml
    - ../Service_Coordinator/deployment.yml
    - ../Service_Document_Template_Helper/deployment.yml
    - ../Service_Document/deployment.yml
    - ../Service_DocumentTemplates/deployment.yml
    - ../Service_ImageProcessing/deployment.yml
    - ../Service_License/deployment.yml
    - ../Service_Notification/deployment.yml
    - ../Service_OCR/deployment.yml
    - ../Service_Payment/deployment.yml
    - ../Service_Report/deployment.yml
profiles:
  - name: local
    activation:
      - kubeContext: docker-desktop
    build:
      local:
        push: false
    deploy:
      kubectl:
        defaultNamespace: backend
      helm:
        releases:
          - name: clerkxpress-backend
            version: 3.0.0
            wait: true
            chartPath: helm
            setValues:
              rabbitmq.enabled: true
              keycloak.enabled: true
              vault.enabled: false
              vizivault-platform.enabled: false
            valuesFiles:
              - helm/local.yaml
  - name: aws-production
    activation:
      - kubeContext: arn:aws:eks:us-east-1:514329541303:cluster/Licensing
    build:
      local:
        push: false
    deploy:
      kubectl:
        defaultNamespace: backend
      helm:
        releases:
          - name: clerkxpress-backend
            version: 3.0.0
            wait: true
            chartPath: helm
            setValues:
              rabbitmq.enabled: true
              keycloak.enabled: true
              vault.enabled: true
              vizivault-platform.enabled: true
            valuesFiles:
              - helm/production.yaml
  - name: aws-staging
    activation:
      - kubeContext: arn:aws:eks:us-east-1:514329541303:cluster/Licensing-Stg
    build:
      local:
        push: false
    deploy:
      helm:
        releases:
          - name: clerkxpress-backend
            version: 3.0.0
            wait: true
            chartPath: helm
            setValues:
              rabbitmq.enabled: true
              keycloak.enabled: true
              vault.enabled: true
              vizivault-platform.enabled: true
            valuesFiles:
              - helm/staging.yaml
      kubectl:
        defaultNamespace: backend
  - name: aws-development
    activation:
      - kubeContext: arn:aws:eks:us-east-1:514329541303:cluster/Licensing-Dev
    build:
      local:
        push: false
    deploy:
      kubectl:
        defaultNamespace: backend
      helm:
        releases:
          - name: clerkxpress-backend
            version: 3.0.0
            wait: true
            chartPath: helm
            setValues:
              rabbitmq.enabled: true
              keycloak.enabled: true
              vault.enabled: true
              vizivault-platform.enabled: true
            valuesFiles:
              - helm/development.yaml
