{"name": "document_template_helper", "version": "1.0.0", "description": "template helper service", "main": "app.js", "scripts": {"start": "nodemon --max-http-header-size=30000 app.js"}, "author": "", "license": "ISC", "dependencies": {"easy-template-x": "^4.1.1", "easy-template-x-angular-expressions": "^0.1.0", "express": "^4.18.2", "helmet": "^6.1.5", "nodemon": "^2.0.22", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.2"}, "repository": {"type": "git", "url": "git+https://github.com/s-Cube-Enterprise/Service_Document_Template_Helper.git"}, "keywords": [], "bugs": {"url": "https://github.com/s-Cube-Enterprise/Service_Document_Template_Helper/issues"}, "homepage": "https://github.com/s-Cube-Enterprise/Service_Document_Template_Helper#readme"}