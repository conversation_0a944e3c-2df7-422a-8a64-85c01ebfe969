const { parentPort, workerData } = require("worker_threads");
const { processDocx } = require("../utils/docxHelper");

(async () => {
    try {
        const { file, data } = workerData;
        const result = await processDocx(file, data);
        parentPort.postMessage({ success: true, result });
    } catch (error) {
        parentPort.postMessage({ success: false, error: error.message });
    }
})();
