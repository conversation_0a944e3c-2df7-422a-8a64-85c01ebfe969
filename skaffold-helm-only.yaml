# A Skaffold File for only triggering and rendering the Helm charts, none of the microservices.
#
# skaffold render -d 514329541303.dkr.ecr.us-east-1.amazonaws.com --file "skaffold-helm-only.yaml" --output helm/charts/vizivault-platform/vizivault-render.yaml -n backend -p aws-development -t latest

apiVersion: skaffold/v4beta2
kind: Config
metadata:
  name: backend
deploy:
  helm:
    releases:
      - name: licensing-backend
        chartPath: ./helm
        wait: true
        namespace: backend
profiles:
  - name: local
    activation:
      - kubeContext: docker-desktop
    build:
      local:
        push: false
    deploy:
      kubectl:
        defaultNamespace: backend
  - name: aws-production
    activation:
      - kubeContext: arn:aws:eks:us-east-1:514329541303:cluster/Licensing
    build:
      local:
        push: false
        tryImportMissing: true
    deploy:
      kubectl:
        defaultNamespace: backend
  - name: aws-staging
    activation:
      - kubeContext: arn:aws:eks:us-east-1:514329541303:cluster/Licensing-Stg
    build:
      local:
        push: false
        tryImportMissing: true
    deploy:
      kubectl:
        defaultNamespace: backend
  - name: aws-development
    activation:
      - kubeContext: arn:aws:eks:us-east-1:514329541303:cluster/Licensing-Dev
    build:
      local:
        push: false
        tryImportMissing: true
    deploy:
      kubectl:
        defaultNamespace: backend
