const express = require("express");
const router = express.Router();
/**
 * @swagger
 * tags:
 *   name: Health Check
 *   description: API for checking server health
 */

/**
 * @swagger
 * /actuator/health:
 *   get:
 *     summary: Check server health
 *     tags: [Health Check]
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           text/plain:
 *             schema:
 *               type: string
 *               example: Server is healthy
 */
router.get("/health", (req, res) => {
    res.status(200).send({
        status: "UP",
    });
});

module.exports = router;
