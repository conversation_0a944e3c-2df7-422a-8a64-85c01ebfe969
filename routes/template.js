const express = require("express");
const { Worker } = require("worker_threads");
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Template Processing
 *   description: API for processing templates
 */

/**
 * @swagger
 * /template:
 *   post:
 *     summary: Process template
 *     tags: [Template Processing]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: The template file to process
 *               data:
 *                 type: string
 *                 description: JSON data to merge with the template
 *             required:
 *               - file
 *               - data
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/octet-stream:
 *             schema:
 *               type: string
 *               format: binary
 *               description: The processed template file as a binary stream
 *       400:
 *         description: Bad Request
 *       500:
 *         description: Internal Server Error
 */

router.get("/", async (req, res) => {
    res.send("Service is up");
});

router.post("/", async (req, res) => {
    const { file, data } = req.body;

    if (!file || !data) {
        return res.status(400).send("File or data is missing");
    }

    let jsonData;
    try {
        jsonData = JSON.parse(data);
    } catch {
        console.log(data);
        return res.status(400).send("Data is not valid JSON");
    }

    // Offload processing to worker thread
    const worker = new Worker(`./workers/docxWorker.js`, {
        workerData: { file, data: jsonData },
    });

    worker.on("message", (message) => {
        console.log("worker processing...");
        if (message.success) {
            console.log("worker processed successfully...");
            res.send(Buffer.from(message.result));
        } else {
            console.error("Worker processing failed:", message.error);
            res.status(500).send({ message: "Failed to process template", error: message.error });
        }
    });

    worker.on("error", (error) => {
        console.error("Worker error:", error);
        res.status(500).send("Internal Server Error");
    });

    worker.on("exit", (code) => {
        if (code !== 0) {
            console.error(`Worker stopped with exit code ${code}`);
        }
    });
});

module.exports = router;
