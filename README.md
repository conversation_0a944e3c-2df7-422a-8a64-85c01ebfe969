# ClerkXpress Kubernetes

## Required Software

- [Skaffold](https://skaffold.dev/)
- [Docker Desktop with Kubernetes engine on](https://www.kindacode.com/article/how-to-enable-kubernetes-in-docker-desktop/)
- [He<PERSON>](https://helm.sh/)
- [Maven 3.6+](https://maven.apache.org/)

## Developing & Deploying Locally with Skaffold

### Prerequisites

The `application.yml` of the services will require to have valid set for services they depend on (user/password for Postgres, etc.). These won't be available in the git repos and will be set via Secrets for AWS deployment so ask <PERSON> for the information.

```yaml
# Postgres
spring:
  application:
    name: PaymentService
  datasource:
    url: "jdbc:postgresql://*************:5432/postgres"

# RabbitMQ
rabbitmq:
  host: rabbitmq.backend.svc.cluster.local
  port: 5672
```

### ECR Login

In order to even render YAMLs, you have to make sure you have access to the AWS Elastic Container Repository (ECR) first. Once you have an AWS account for the company's cloud, installed the AWS CLI tool and have run the `aws configure` you can run the following:

```shell
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com
```

### Adding Secrets

Secrets allow us to commit the configurations for the deployment without the actual password values associated with them. However, it does mean having to run a few extra commands in order to run things locally. You must change the values in `<>` accordingly with the passwords provided by someone.

```shell
kubectl create secret generic rabbitmq-user-pass-secret --namespace=backend --from-literal=username=<RABBITMQ USERNAME> --from-literal=password=<RABBITMQ PASSWORD>
```

```shell
kubectl create secret generic postgres-user-pass-secret --namespace=backend --from-literal=username=<POSTGRES USERNAME> --from-literal=password=<POSTGRES PASSWORD>
```

```shell
kubectl create secret generic keycloak-user-pass-secret --namespace=backend --from-literal=username=<KEYCLOAK USERNAME> --from-literal=password=<KEYCLOAK PASSWORD>
```

```shell
kubectl create secret generic open-ai-token --namespace=backend --from-literal=name=openai --from-literal=<TOKEN VALUE>
```

For more information on secets check out their [documentation](https://kubernetes.io/docs/tasks/configmap-secret/managing-secret-using-kubectl/).

## Running

With the current format of the Kubernetes Configuration project, working locally requires all the services to be pulled from GitHub and placed in the same directory as the `/ClerkXpress-Kubernetes`. This way, the default function of Skaffold can watch for changes and update the local images running.

Example directory format:

```
.
├── ClerkXpress-Kubernetes
├── Service_Auth
├── Service_Calculation
├── Service_Config
├── Service_Document
├── Service_Document_Templates
├── Service_License
├── Service_Notification
├── Service_Payment
├── Service_Report
├── Service_AI
├── Service_ImageProcessing
└── Service_OCR
```

Part of Skaffold's functionality is to push images to a image repository. This isn't exactly something we're intending to use the system for so it's best to avoid that by running the `local` profile for Skaffold via the following command:

```shell
skaffold dev -p local
```

Kuberentes keeps the networking between services isolated from the host machine by default for good reason. However, there will be times in which access to certain portions of the stack from your computer is desired. By running the same command with the port forward flag, it'll expose all the Swagger pages, APIs, RabbitMQ, Postgres etc.

```shell
skaffold dev -p local --port-forward
```

> **_NOTE:_** For simplicity, `.bat` and `.sh` scripts have been added to the directory and should be all that you need.

### Only Running Dependency Services

If you're like me when developing an application, running the entire software stack may seem excessive. To alliviate that, There is an additional Skaffold Config in the project that allows for running only the services the [s]Cube applications depend on rather than all. You can use this in conjunction with running your application from your IDE/command line to keep development focused.

```shell
skaffold dev -p dependencies --port-forward --auto-build=false --auto-deploy=false
```

> **_NOTE:_** The Skaffold command still runs the file watching but the --auto-build & --auto-deploy flags will stop Skaffold from wasting time rebuilding the images.

### Cleanup

If you want to wipe out anything deployed via Skaffold minus the persistent volumes, call the delete command to get a clean start next time.

```shell
skaffold delete
```

There is also a script of this too.

### Service Links

The default URLs for these when run with the port forward flag:

- [Auth Service Swagger UI](http://localhost:9001/swagger-ui/index.html#%2F)
- [Calculation Service Swagger UI](http://localhost:9002/swagger-ui/index.html#%2F)
- [Documents Service Swagger UI](http://localhost:9003/swagger-ui/index.html#%2F)
- [License Service Swagger UI](http://localhost:9004/swagger-ui/index.html#%2F)
- [Notification Service Swagger UI](http://localhost:9005/swagger-ui/index.html#%2F)
- [Payment Service Swagger UI](http://localhost:9006/swagger-ui/index.html#%2F)
- [Report Service Swagger UI](http://localhost:9007/swagger-ui/index.html#%2F)
- [OCR Service Swagger UI](http://localhost:9008/swagger/index.html#%2F)
- [Document Templates Service Swagger UI](http://localhost:9009/swagger/index.html#%2F)
- [RabbitMQ Admin UI](http://localhost:15672/)
- [Postgres Port](http://localhost:5432/)
- [Keycloak](http://localhost:10002/)

## Deploying to AWS

Here are the steps for manually deploying the service stack to AWS. This serves as a basic starting point to be built on and exanded to proper CI/CD in the future.

1. Make sure you have proper access to the EKS enviornment on AWS.

  ```shell
  aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com
  ```

2. Change to AWS EKS context

  ```shell
  kubectl config use-context arn:aws:eks:us-east-1:************:cluster/Licensing-Dev
  ```

3. In order for Kubernentes to have the access to pull images from ECR, a secret must be added to the namespace targetted. Here is an example for the `backend` namespace.

  ```shell
  $PASSWORD=aws ecr get-login-password --region us-east-1
  ```

  ```shell
  kubectl create secret docker-registry aws-ecr-secret --docker-server=https://<EMAIL> --docker-username=AWS --docker-password=$PASSWORD -n backend
  ```

4. The `image-pull-secret` must be applied to the applications being deployed in their **Deployment**.

  ```yaml
  ...
  spec:
    ...
    template:
      ...
      spec:
        imagePullSecrets:
          - name: aws-ecr-secret
        ...
  ```

5. All Secrets must be created in the namespace

  - Refer to [Adding Secrets](#adding-secrets) above

6. Use Skaffold's `render` command to generate the Kubernentes definitions. Additional flags for namespace and profile advised.

   ```shell
   skaffold render --output rendered-pod.yaml -n backend -p aws -t latest --default-repo ************.dkr.ecr.us-east-1.amazonaws.com
   ```

   If you want to skip the build phase and use existing tags (`latest`):


   ```shell
  skaffold render --output rendered-pod.yaml -n backend -p aws -t latest --default-repo ************.dkr.ecr.us-east-1.amazonaws.com --digest-source=tag --loud=true
   ```

7. Apply Ingress-Nginx to the cluster and then apply our endpoint definitions to that

  ```shell
  kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.7.0/deploy/static/provider/aws/deploy.yaml
  ```

  ```shell
  kubectl apply -f ingress.yaml
  ```

8. Apply the newly rendered definitions.

  ```shell
  skaffold apply rendered-pod.yaml -p aws -n backend
  ```

## Troubleshooting

- The vault container is erroring!

  - Almost certainly you will need to head [here](https://developer.hashicorp.com/vault/tutorials/getting-started/getting-started-deploy#initializing-the-vault)/[here](https://developer.hashicorp.com/vault/tutorials/kubernetes/kubernetes-raft-deployment-guide#initialize-and-unseal-vault) and initialize the vault for the first time. The command is run inside the container itself.
    - For ease of use, there's a [UI webpage](http://localhost:8200/) in which to unseal the vault at port 8200

  > **Be sure to follow the instructions closely. You'll lose access to everything saved there on your local machine otherwise.**

- Things aren't working right or something is horribly stuck!

  - The easiest and most straight forward solution is to nuke the `backend` namespace everything should be deployed to and re-deploy things. You'll need to readd the secrets but have those stored somewhere save for easily reapply is expected. You will also lose any saved data in the Vault or Postgres DB so **be careful**!

    ```shell
    kubectl delete namespace backend
    ```

- Docker is giving me a 404!
    ![image](https://github.com/s-Cube-Enterprise/ClerkXpress-Kubernetes/assets/51640895/9193bbd9-6fb9-4d15-bda1-accd1424a06f)


  - If you get a 401 registry error it is because it is trying to push your images to Docker Hub. You may want to run the below command to avoid a 401 registry error. (https://github.com/drud/ddev/issues/1689)

    ```shell
    docker logout
    ```
  - follow this [stackoverflow](https://stackoverflow.com/a/74306078/11703800) for help
    - you basically delete any docker credentials from Credentials Manager (Control Panel - search for Credential Manager (under Generic Credentials))
    - in powership type `docker login`

- I'm getting the following! `Error: uninstall: Release not loaded: licensing-backend: release: not found`

  - This is thrown when the skaffold command is run in the wrong namespace. If you're not using included batch/shell scripts you likely forgot the `-n` flag.
