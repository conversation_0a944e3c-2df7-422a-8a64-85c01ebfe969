const easy_temp = require("easy-template-x");
const { createResolver } = require("easy-template-x-angular-expressions");

const handler = new easy_temp.TemplateHandler({
    scopeDataResolver: createResolver(),
});

// Convert base64 images to Buffers in the JSON data
const dataImageToBuffer = (data) => {
    const newData = { ...data };
    const traverseAndConvert = (obj) => {
        if (!obj) return; // Handle null or undefined objects
        for (const key in obj) {
            if (typeof obj[key] === "object") {
                if (obj[key] && obj[key]._type === "image" && obj[key].source) {
                    obj[key].source = Buffer.from(obj[key].source, "base64");
                } else {
                    traverseAndConvert(obj[key]);
                }
            }
        }
    };
    traverseAndConvert(newData);
    return newData;
};

// Process DOCX template with data
const processDocx = async (file, data) => {
    const inputFile = Buffer.from(file, "base64");
    const dataBuffer = dataImageToBuffer(data);

    console.time("processing file. . .");
    const result = await handler.process(inputFile, dataBuffer);
    console.timeEnd("processing file. . .");

    return result;
};

module.exports = {
    processDocx,
};
