-- Startup script for the Postgres docker-compose
-- This and any other SQL files in the directory will run on start up.
--   Destroy and recreate the volumes if the docker-compose has already been ran.

-- Databases
CREATE DATABASE auth;
CREATE DATABASE ai;
CREATE DATABASE calculation;
CREATE DATABASE config;
CREATE DATABASE document;
CREATE DATABASE documenttemplates;
CREATE DATABASE license;
CREATE DATABASE notification;
CREATE DATABASE payment;
CREATE DATABASE report;

\c auth;
CREATE SCHEMA keycloak;
