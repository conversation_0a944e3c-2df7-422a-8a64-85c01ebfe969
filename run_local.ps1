# Turn off the echo
$ErrorActionPreference = "SilentlyContinue"

# Change contexts to never screw with the AWS EKS clusters
kubectl config use-context docker-desktop

# Make any namespaces needed
kubectl create namespace backend
kubectl create namespace frontend
kubectl create namespace keycloak
kubectl create namespace ai-sandbox
kubectl create namespace hashicorp-vault
kubectl create namespace vizivault

# Log into AWS ECR for image pulling
$ecrPassword = aws ecr get-login-password --region us-east-1
docker login --username AWS --password $ecrPassword 514329541303.dkr.ecr.us-east-1.amazonaws.com

# Refresh AWS CodeArtifact token for Maven
$env:CODEARTIFACT_AUTH_TOKEN = aws codeartifact get-authorization-token --domain scube --domain-owner 514329541303 --region us-east-1 --query authorizationToken --output text

# Run stack via Skaffold
skaffold dev -p local -n backend --port-forward --skip-tests

# Pause the script
Read-Host -Prompt "Press Enter to exit"
