FROM node:23-alpine3.20 AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --production
COPY . .
RUN npx @vercel/ncc build app.js -o dist
RUN npx  @vercel/ncc build workers/docxWorker.js -o dist/workers/docxWorker.js
# RUN npx esbuild app.js --bundle --platform=node --outfile=dist/index.js

FROM alpine:3.20
RUN apk add --no-cache nodejs && \
    addgroup --system --gid 1001 node && \
    adduser --system --uid 1001 node && \
    mkdir -p /app && \
    chown -R node:node /app
WORKDIR /app
COPY --from=builder --chown=node:node /app/dist .
USER node
EXPOSE 9012
# memory limit
ENV NODE_OPTIONS="--max-old-space-size=5120" 
ENV PORT=9012
CMD ["node", "index.js"]